"""
SAMPLE CODE - Unit tests for Named Entity Recognition component
"""

import pytest
import spacy
from spacy.tokens import Doc
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from pipeline.ner_component import SchedulerNERComponent


class TestSchedulerNERComponent:
    """Test cases for SchedulerNERComponent."""
    
    @pytest.fixture
    def nlp(self):
        """Create spaCy nlp instance for testing."""
        return spacy.blank("en")
    
    @pytest.fixture
    def ner_component(self, nlp):
        """Create NER component for testing."""
        return SchedulerNERComponent(nlp)
    
    def test_component_initialization(self, nlp):
        """Test component initialization."""
        component = SchedulerNERComponent(nlp)
        assert component.nlp == nlp
        assert component.name == "scheduler_ner"
        assert isinstance(component.entity_types, dict)
        assert len(component.entity_types) > 0
    
    def test_entity_types_defined(self, ner_component):
        """Test that entity types are properly defined."""
        expected_types = ['PERSON', 'DATE', 'TIME', 'LOCATION', 'TASK', 'DURATION', 'FREQUENCY']
        
        for entity_type in expected_types:
            assert entity_type in ner_component.entity_types
            assert isinstance(ner_component.entity_types[entity_type], str)
    
    def test_call_method_returns_doc(self, nlp, ner_component):
        """Test that __call__ method returns a Doc object."""
        text = "Schedule a meeting with John tomorrow at 2 PM"
        doc = nlp(text)
        
        result = ner_component(doc)
        
        assert isinstance(result, Doc)
        assert result.text == text
    
    def test_rule_based_extraction(self, nlp, ner_component):
        """Test rule-based entity extraction."""
        text = "Schedule a meeting with John Smith tomorrow at 2 PM in the conference room"
        doc = nlp(text)
        
        # Process through component
        result = ner_component(doc)
        
        # Should have some entities extracted
        assert len(result.ents) > 0
        
        # Check for expected entity types
        entity_labels = [ent.label_ for ent in result.ents]
        assert any(label in ['PERSON', 'TIME', 'TASK', 'LOCATION'] for label in entity_labels)
    
    def test_time_expression_extraction(self, nlp, ner_component):
        """Test extraction of time expressions."""
        test_cases = [
            "Meet at 2:30 PM",
            "Call at 9 AM",
            "Lunch at noon",
            "Meeting in the morning"
        ]
        
        for text in test_cases:
            doc = nlp(text)
            result = ner_component(doc)
            
            # Should extract at least one TIME entity
            time_entities = [ent for ent in result.ents if ent.label_ == 'TIME']
            assert len(time_entities) > 0, f"No TIME entity found in: {text}"
    
    def test_task_expression_extraction(self, nlp, ner_component):
        """Test extraction of task expressions."""
        test_cases = [
            "Schedule a meeting",
            "Book an appointment",
            "Set up a conference call",
            "Plan a workshop"
        ]
        
        for text in test_cases:
            doc = nlp(text)
            result = ner_component(doc)
            
            # Should extract at least one TASK entity
            task_entities = [ent for ent in result.ents if ent.label_ == 'TASK']
            assert len(task_entities) > 0, f"No TASK entity found in: {text}"
    
    def test_location_expression_extraction(self, nlp, ner_component):
        """Test extraction of location expressions."""
        test_cases = [
            "Meet in the conference room",
            "Call from the office",
            "Meeting in building A"
        ]
        
        for text in test_cases:
            doc = nlp(text)
            result = ner_component(doc)
            
            # Should extract at least one LOCATION entity
            location_entities = [ent for ent in result.ents if ent.label_ == 'LOCATION']
            assert len(location_entities) > 0, f"No LOCATION entity found in: {text}"
    
    def test_remove_overlaps(self, nlp, ner_component):
        """Test that overlapping entities are properly handled."""
        from spacy.tokens import Span
        
        text = "Schedule a meeting tomorrow"
        doc = nlp(text)
        
        # Create overlapping spans
        span1 = Span(doc, 0, 2, label="TASK")  # "Schedule a"
        span2 = Span(doc, 1, 3, label="TASK")  # "a meeting"
        
        overlapping_entities = [span1, span2]
        non_overlapping = ner_component._remove_overlaps(overlapping_entities)
        
        # Should remove one of the overlapping entities
        assert len(non_overlapping) == 1
    
    def test_get_entity_info(self, nlp, ner_component):
        """Test entity information extraction."""
        text = "Schedule a meeting with John tomorrow at 2 PM"
        doc = nlp(text)
        
        # Process through component
        result = ner_component(doc)
        
        # Get entity information
        entity_info = ner_component.get_entity_info(result)
        
        assert isinstance(entity_info, list)
        
        for info in entity_info:
            assert isinstance(info, dict)
            assert 'text' in info
            assert 'label' in info
            assert 'start' in info
            assert 'end' in info
            assert 'description' in info
            
            # Validate data types
            assert isinstance(info['text'], str)
            assert isinstance(info['label'], str)
            assert isinstance(info['start'], int)
            assert isinstance(info['end'], int)
            assert isinstance(info['description'], str)
    
    def test_empty_text(self, nlp, ner_component):
        """Test handling of empty text."""
        text = ""
        doc = nlp(text)
        
        result = ner_component(doc)
        
        assert isinstance(result, Doc)
        assert len(result.ents) == 0
    
    def test_no_entities_text(self, nlp, ner_component):
        """Test handling of text with no recognizable entities."""
        text = "This is just some random text without scheduling content"
        doc = nlp(text)
        
        result = ner_component(doc)
        
        assert isinstance(result, Doc)
        # May or may not have entities depending on base model
    
    def test_component_with_trained_model(self, nlp):
        """Test component initialization with trained model path."""
        # Test with non-existent model path
        component = SchedulerNERComponent(nlp, model_path="non_existent_path")
        
        # Should initialize without error but ner_model should be None
        assert component.ner_model is None
    
    def test_multiple_entity_types_in_text(self, nlp, ner_component):
        """Test extraction of multiple entity types from single text."""
        text = "Schedule a meeting with John Smith tomorrow at 2 PM in the conference room"
        doc = nlp(text)
        
        result = ner_component(doc)
        
        # Should extract multiple types of entities
        entity_labels = set(ent.label_ for ent in result.ents)
        
        # Expect at least 2 different entity types
        assert len(entity_labels) >= 2
    
    @pytest.mark.parametrize("text,expected_labels", [
        ("Meet John at 2 PM", ["PERSON", "TIME"]),
        ("Conference room meeting", ["LOCATION", "TASK"]),
        ("Daily standup every Monday", ["TASK", "DATE"]),
    ])
    def test_specific_entity_extractions(self, nlp, ner_component, text, expected_labels):
        """Test specific entity extractions with parameterized inputs."""
        doc = nlp(text)
        result = ner_component(doc)
        
        extracted_labels = [ent.label_ for ent in result.ents]
        
        # Check that at least some expected labels are found
        found_expected = any(label in extracted_labels for label in expected_labels)
        assert found_expected, f"None of {expected_labels} found in {extracted_labels} for text: {text}"


if __name__ == "__main__":
    pytest.main([__file__])
