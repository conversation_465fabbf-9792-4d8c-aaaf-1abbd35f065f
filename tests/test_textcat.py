"""
SAMPLE CODE - Unit tests for Text Classification component
"""

import pytest
import spacy
from spacy.tokens import Doc
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from pipeline.textcat_component import SchedulerTextCatComponent


class TestSchedulerTextCatComponent:
    """Test cases for SchedulerTextCatComponent."""
    
    @pytest.fixture
    def nlp(self):
        """Create spaCy nlp instance for testing."""
        return spacy.blank("en")
    
    @pytest.fixture
    def textcat_component(self, nlp):
        """Create text classification component for testing."""
        return SchedulerTextCatComponent(nlp)
    
    def test_component_initialization(self, nlp):
        """Test component initialization."""
        component = SchedulerTextCatComponent(nlp)
        assert component.nlp == nlp
        assert component.name == "scheduler_textcat"
        assert isinstance(component.categories, dict)
        assert len(component.categories) > 0
    
    def test_categories_defined(self, textcat_component):
        """Test that categories are properly defined."""
        expected_categories = [
            'MEETING', 'REMINDER', 'DEADLINE', 'RECURRING', 
            'CANCELLATION', 'APPOINTMENT', 'TASK', 'OTHER'
        ]
        
        for category in expected_categories:
            assert category in textcat_component.categories
            assert 'description' in textcat_component.categories[category]
            assert 'keywords' in textcat_component.categories[category]
            assert 'priority' in textcat_component.categories[category]
    
    def test_doc_extensions_added(self, nlp, textcat_component):
        """Test that Doc extensions are added."""
        text = "Test text"
        doc = nlp(text)
        
        # Extensions should be available
        assert hasattr(doc._, 'categories')
        assert hasattr(doc._, 'primary_category')
        assert hasattr(doc._, 'category_confidence')
        
        # Default values
        assert doc._.categories == {}
        assert doc._.primary_category == "OTHER"
        assert doc._.category_confidence == 0.0
    
    def test_call_method_returns_doc(self, nlp, textcat_component):
        """Test that __call__ method returns a Doc object with classifications."""
        text = "Schedule a meeting with John tomorrow"
        doc = nlp(text)
        
        result = textcat_component(doc)
        
        assert isinstance(result, Doc)
        assert hasattr(result._, 'categories')
        assert hasattr(result._, 'primary_category')
        assert hasattr(result._, 'category_confidence')
        
        # Should have classification results
        assert isinstance(result._.categories, dict)
        assert len(result._.categories) > 0
        assert isinstance(result._.primary_category, str)
        assert isinstance(result._.category_confidence, float)
    
    def test_rule_based_classification(self, nlp, textcat_component):
        """Test rule-based classification."""
        text = "Schedule a meeting with John tomorrow"
        doc = nlp(text)
        
        categories = textcat_component._rule_based_classification(doc)
        
        assert isinstance(categories, dict)
        assert len(categories) > 0
        
        # All scores should be between 0 and 1
        for category, score in categories.items():
            assert 0.0 <= score <= 1.0
        
        # Scores should sum to approximately 1.0
        total_score = sum(categories.values())
        assert abs(total_score - 1.0) < 0.001
    
    def test_meeting_classification(self, nlp, textcat_component):
        """Test classification of meeting-related text."""
        meeting_texts = [
            "Schedule a meeting with John",
            "Book a conference call",
            "Set up a discussion session"
        ]
        
        for text in meeting_texts:
            doc = nlp(text)
            result = textcat_component(doc)
            
            # Should classify as MEETING or have high MEETING score
            assert result._.primary_category in ['MEETING', 'APPOINTMENT', 'TASK']
            assert result._.categories.get('MEETING', 0) > 0
    
    def test_reminder_classification(self, nlp, textcat_component):
        """Test classification of reminder-related text."""
        reminder_texts = [
            "Remind me to call John",
            "Set a notification for tomorrow",
            "Alert me about the deadline"
        ]
        
        for text in reminder_texts:
            doc = nlp(text)
            result = textcat_component(doc)
            
            # Should have high REMINDER score
            assert result._.categories.get('REMINDER', 0) > 0
    
    def test_cancellation_classification(self, nlp, textcat_component):
        """Test classification of cancellation-related text."""
        cancellation_texts = [
            "Cancel my meeting with John",
            "Remove the appointment",
            "Delete the scheduled call"
        ]
        
        for text in cancellation_texts:
            doc = nlp(text)
            result = textcat_component(doc)
            
            # Should classify as CANCELLATION or have high CANCELLATION score
            assert result._.categories.get('CANCELLATION', 0) > 0
    
    def test_recurring_classification(self, nlp, textcat_component):
        """Test classification of recurring event text."""
        recurring_texts = [
            "Set up a weekly meeting",
            "Schedule daily standup",
            "Book monthly review"
        ]
        
        for text in recurring_texts:
            doc = nlp(text)
            result = textcat_component(doc)
            
            # Should have high RECURRING score
            assert result._.categories.get('RECURRING', 0) > 0
    
    def test_deadline_classification(self, nlp, textcat_component):
        """Test classification of deadline-related text."""
        deadline_texts = [
            "Project deadline is Friday",
            "Submit report by tomorrow",
            "Complete task before 5 PM"
        ]
        
        for text in deadline_texts:
            doc = nlp(text)
            result = textcat_component(doc)
            
            # Should have high DEADLINE score
            assert result._.categories.get('DEADLINE', 0) > 0
    
    def test_get_classification_details(self, nlp, textcat_component):
        """Test detailed classification information."""
        text = "Schedule a meeting with John tomorrow"
        doc = nlp(text)
        result = textcat_component(doc)
        
        details = textcat_component.get_classification_details(result)
        
        assert isinstance(details, dict)
        assert 'primary_category' in details
        assert 'confidence' in details
        assert 'all_categories' in details
        assert 'sorted_categories' in details
        assert 'category_info' in details
        assert 'priority' in details
        assert 'top_3_categories' in details
        
        # Validate data types
        assert isinstance(details['primary_category'], str)
        assert isinstance(details['confidence'], float)
        assert isinstance(details['all_categories'], dict)
        assert isinstance(details['sorted_categories'], list)
        assert isinstance(details['category_info'], dict)
        assert isinstance(details['priority'], str)
        assert isinstance(details['top_3_categories'], list)
        
        # Top 3 categories should be sorted by score
        top_3 = details['top_3_categories']
        assert len(top_3) <= 3
        for i in range(len(top_3) - 1):
            assert top_3[i][1] >= top_3[i + 1][1]
    
    def test_classify_batch(self, nlp, textcat_component):
        """Test batch classification."""
        texts = [
            "Schedule a meeting",
            "Remind me to call",
            "Cancel appointment"
        ]
        
        results = textcat_component.classify_batch(texts)
        
        assert isinstance(results, list)
        assert len(results) == len(texts)
        
        for i, result in enumerate(results):
            assert isinstance(result, dict)
            assert 'text' in result
            assert 'classification' in result
            assert result['text'] == texts[i]
            assert isinstance(result['classification'], dict)
    
    def test_get_category_statistics(self, nlp, textcat_component):
        """Test category statistics calculation."""
        texts = [
            "Schedule a meeting",
            "Schedule another meeting",
            "Remind me to call",
            "Cancel appointment"
        ]
        
        docs = []
        for text in texts:
            doc = nlp(text)
            doc = textcat_component(doc)
            docs.append(doc)
        
        stats = textcat_component.get_category_statistics(docs)
        
        assert isinstance(stats, dict)
        assert 'total_documents' in stats
        assert 'category_counts' in stats
        assert 'category_percentages' in stats
        assert 'average_confidence' in stats
        assert 'confidence_distribution' in stats
        
        # Validate statistics
        assert stats['total_documents'] == len(texts)
        assert isinstance(stats['category_counts'], dict)
        assert isinstance(stats['category_percentages'], dict)
        assert isinstance(stats['average_confidence'], float)
        assert isinstance(stats['confidence_distribution'], dict)
        
        # Percentages should sum to 100
        total_percentage = sum(stats['category_percentages'].values())
        assert abs(total_percentage - 100.0) < 0.001
    
    def test_empty_text_classification(self, nlp, textcat_component):
        """Test classification of empty text."""
        text = ""
        doc = nlp(text)
        result = textcat_component(doc)
        
        # Should still have classification results
        assert isinstance(result._.categories, dict)
        assert len(result._.categories) > 0
        assert isinstance(result._.primary_category, str)
        assert isinstance(result._.category_confidence, float)
    
    def test_unknown_text_classification(self, nlp, textcat_component):
        """Test classification of text with no clear category."""
        text = "This is just random text with no scheduling content"
        doc = nlp(text)
        result = textcat_component(doc)
        
        # Should classify as OTHER or have low confidence
        assert isinstance(result._.primary_category, str)
        # OTHER category should have some score
        assert result._.categories.get('OTHER', 0) >= 0
    
    def test_confidence_scores_valid(self, nlp, textcat_component):
        """Test that confidence scores are valid."""
        texts = [
            "Schedule a meeting",
            "Remind me to call",
            "Cancel appointment",
            "Random text"
        ]
        
        for text in texts:
            doc = nlp(text)
            result = textcat_component(doc)
            
            # Confidence should be between 0 and 1
            assert 0.0 <= result._.category_confidence <= 1.0
            
            # All category scores should be between 0 and 1
            for category, score in result._.categories.items():
                assert 0.0 <= score <= 1.0
    
    @pytest.mark.parametrize("text,expected_high_categories", [
        ("Schedule a meeting with John", ["MEETING", "APPOINTMENT"]),
        ("Remind me to call tomorrow", ["REMINDER"]),
        ("Cancel my appointment", ["CANCELLATION"]),
        ("Weekly team standup", ["RECURRING", "MEETING"]),
        ("Project deadline Friday", ["DEADLINE"]),
    ])
    def test_specific_classifications(self, nlp, textcat_component, text, expected_high_categories):
        """Test specific classifications with parameterized inputs."""
        doc = nlp(text)
        result = textcat_component(doc)
        
        # At least one of the expected categories should have a high score
        high_score_found = False
        for category in expected_high_categories:
            if result._.categories.get(category, 0) > 0.1:  # Threshold for "high" score
                high_score_found = True
                break
        
        assert high_score_found, f"None of {expected_high_categories} had high score for: {text}"
    
    def test_component_with_trained_model(self, nlp):
        """Test component initialization with trained model path."""
        # Test with non-existent model path
        component = SchedulerTextCatComponent(nlp, model_path="non_existent_path")
        
        # Should initialize without error but textcat_model should be None
        assert component.textcat_model is None


if __name__ == "__main__":
    pytest.main([__file__])
