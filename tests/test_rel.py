"""
SAMPLE CODE - Unit tests for Relation Extraction component
"""

import pytest
import spacy
from spacy.tokens import Doc, Span
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from pipeline.rel_component import SchedulerRelationComponent
from pipeline.ner_component import SchedulerNERComponent


class TestSchedulerRelationComponent:
    """Test cases for SchedulerRelationComponent."""
    
    @pytest.fixture
    def nlp(self):
        """Create spaCy nlp instance for testing."""
        nlp = spacy.blank("en")
        # Add NER component first since relations depend on entities
        ner_component = SchedulerNERComponent(nlp)
        nlp.add_pipe(ner_component, name="scheduler_ner")
        return nlp
    
    @pytest.fixture
    def rel_component(self, nlp):
        """Create relation component for testing."""
        return SchedulerRelationComponent(nlp)
    
    def test_component_initialization(self, nlp):
        """Test component initialization."""
        component = SchedulerRelationComponent(nlp)
        assert component.nlp == nlp
        assert component.name == "scheduler_relations"
        assert isinstance(component.relation_types, dict)
        assert len(component.relation_types) > 0
    
    def test_relation_types_defined(self, rel_component):
        """Test that relation types are properly defined."""
        expected_types = [
            'SCHEDULED_FOR', 'AT_TIME', 'AT_LOCATION', 'WITH_PERSON',
            'BEFORE_TIME', 'AFTER_TIME', 'RECURRING_ON', 'DURATION_OF', 'ASSIGNED_TO'
        ]
        
        for relation_type in expected_types:
            assert relation_type in rel_component.relation_types
            assert isinstance(rel_component.relation_types[relation_type], str)
    
    def test_doc_extension_added(self, nlp, rel_component):
        """Test that Doc extension for relations is added."""
        text = "Test text"
        doc = nlp(text)
        
        # Extension should be available
        assert hasattr(doc._, 'relations')
        assert doc._.relations == []
    
    def test_call_method_returns_doc(self, nlp, rel_component):
        """Test that __call__ method returns a Doc object with relations."""
        text = "Schedule a meeting with John tomorrow at 2 PM"
        doc = nlp(text)  # This will run through NER first
        
        result = rel_component(doc)
        
        assert isinstance(result, Doc)
        assert hasattr(result._, 'relations')
        assert isinstance(result._.relations, list)
    
    def test_extract_relations_with_entities(self, nlp, rel_component):
        """Test relation extraction when entities are present."""
        text = "Schedule a meeting with John tomorrow at 2 PM"
        doc = nlp(text)  # Process through NER first
        
        # Manually add some entities for testing
        from spacy.tokens import Span
        entities = [
            Span(doc, 2, 3, label="TASK"),      # "meeting"
            Span(doc, 5, 6, label="PERSON"),    # "John"
            Span(doc, 6, 7, label="DATE"),      # "tomorrow"
            Span(doc, 9, 11, label="TIME")      # "2 PM"
        ]
        doc.ents = entities
        
        result = rel_component(doc)
        relations = result._.relations
        
        # Should extract some relations
        assert len(relations) > 0
        
        # Check relation structure
        for relation in relations:
            assert 'head' in relation
            assert 'tail' in relation
            assert 'head_entity' in relation
            assert 'tail_entity' in relation
            assert 'relation' in relation
            assert 'confidence' in relation
    
    def test_scheduled_for_relation(self, nlp, rel_component):
        """Test SCHEDULED_FOR relation extraction."""
        text = "Meeting with John on Friday"
        doc = nlp(text)
        
        # Add entities manually
        from spacy.tokens import Span
        entities = [
            Span(doc, 0, 1, label="TASK"),      # "Meeting"
            Span(doc, 2, 3, label="PERSON"),    # "John"
            Span(doc, 4, 5, label="DATE")       # "Friday"
        ]
        doc.ents = entities
        
        result = rel_component(doc)
        relations = result._.relations
        
        # Should find SCHEDULED_FOR relation
        scheduled_relations = [r for r in relations if r['relation'] == 'SCHEDULED_FOR']
        assert len(scheduled_relations) > 0
    
    def test_at_time_relation(self, nlp, rel_component):
        """Test AT_TIME relation extraction."""
        text = "Meeting at 2 PM"
        doc = nlp(text)
        
        # Add entities manually
        from spacy.tokens import Span
        entities = [
            Span(doc, 0, 1, label="TASK"),      # "Meeting"
            Span(doc, 2, 4, label="TIME")       # "2 PM"
        ]
        doc.ents = entities
        
        result = rel_component(doc)
        relations = result._.relations
        
        # Should find AT_TIME relation
        time_relations = [r for r in relations if r['relation'] == 'AT_TIME']
        assert len(time_relations) > 0
    
    def test_with_person_relation(self, nlp, rel_component):
        """Test WITH_PERSON relation extraction."""
        text = "Meeting with John"
        doc = nlp(text)
        
        # Add entities manually
        from spacy.tokens import Span
        entities = [
            Span(doc, 0, 1, label="TASK"),      # "Meeting"
            Span(doc, 2, 3, label="PERSON")     # "John"
        ]
        doc.ents = entities
        
        result = rel_component(doc)
        relations = result._.relations
        
        # Should find WITH_PERSON relation
        person_relations = [r for r in relations if r['relation'] == 'WITH_PERSON']
        assert len(person_relations) > 0
    
    def test_confidence_calculation(self, nlp, rel_component):
        """Test confidence score calculation."""
        text = "Meeting with John at 2 PM"
        doc = nlp(text)
        
        # Add entities manually
        from spacy.tokens import Span
        entities = [
            Span(doc, 0, 1, label="TASK"),      # "Meeting"
            Span(doc, 2, 3, label="PERSON"),    # "John"
            Span(doc, 4, 6, label="TIME")       # "2 PM"
        ]
        doc.ents = entities
        
        result = rel_component(doc)
        relations = result._.relations
        
        # All relations should have confidence scores
        for relation in relations:
            assert 'confidence' in relation
            assert 0.0 <= relation['confidence'] <= 1.0
            assert isinstance(relation['confidence'], float)
    
    def test_no_entities_no_relations(self, nlp, rel_component):
        """Test that no relations are extracted when no entities are present."""
        text = "This is just plain text"
        doc = nlp(text)
        doc.ents = []  # Ensure no entities
        
        result = rel_component(doc)
        relations = result._.relations
        
        assert len(relations) == 0
    
    def test_single_entity_no_relations(self, nlp, rel_component):
        """Test that no relations are extracted with only one entity."""
        text = "Meeting"
        doc = nlp(text)
        
        # Add single entity
        from spacy.tokens import Span
        entities = [Span(doc, 0, 1, label="TASK")]
        doc.ents = entities
        
        result = rel_component(doc)
        relations = result._.relations
        
        assert len(relations) == 0
    
    def test_get_relations_summary(self, nlp, rel_component):
        """Test relations summary generation."""
        text = "Meeting with John tomorrow at 2 PM"
        doc = nlp(text)
        
        # Add entities manually
        from spacy.tokens import Span
        entities = [
            Span(doc, 0, 1, label="TASK"),      # "Meeting"
            Span(doc, 2, 3, label="PERSON"),    # "John"
            Span(doc, 3, 4, label="DATE"),      # "tomorrow"
            Span(doc, 5, 7, label="TIME")       # "2 PM"
        ]
        doc.ents = entities
        
        result = rel_component(doc)
        summary = rel_component.get_relations_summary(result)
        
        assert isinstance(summary, dict)
        assert 'total_relations' in summary
        assert 'relation_types' in summary
        assert 'high_confidence_relations' in summary
        assert 'entities_involved' in summary
        
        assert isinstance(summary['total_relations'], int)
        assert isinstance(summary['relation_types'], dict)
        assert isinstance(summary['high_confidence_relations'], list)
        assert isinstance(summary['entities_involved'], list)
    
    def test_visualize_relations(self, nlp, rel_component):
        """Test relations visualization."""
        text = "Meeting with John at 2 PM"
        doc = nlp(text)
        
        # Add entities manually
        from spacy.tokens import Span
        entities = [
            Span(doc, 0, 1, label="TASK"),      # "Meeting"
            Span(doc, 2, 3, label="PERSON"),    # "John"
            Span(doc, 4, 6, label="TIME")       # "2 PM"
        ]
        doc.ents = entities
        
        result = rel_component(doc)
        visualization = rel_component.visualize_relations(result)
        
        assert isinstance(visualization, str)
        assert len(visualization) > 0
        
        if result._.relations:
            assert "Extracted Relations:" in visualization
        else:
            assert "No relations found." in visualization
    
    def test_classify_relation_edge_cases(self, nlp, rel_component):
        """Test relation classification edge cases."""
        text = "A B"
        doc = nlp(text)
        
        # Add entities with no clear relation
        from spacy.tokens import Span
        ent1 = Span(doc, 0, 1, label="OTHER")
        ent2 = Span(doc, 1, 2, label="OTHER")
        
        relation = rel_component._classify_relation(doc, ent1, ent2)
        
        # Should return None for unclear relations
        assert relation is None
    
    def test_before_after_time_relations(self, nlp, rel_component):
        """Test BEFORE_TIME and AFTER_TIME relation extraction."""
        test_cases = [
            ("Call before 5 PM", "BEFORE_TIME"),
            ("Meeting after lunch", "AFTER_TIME")
        ]
        
        for text, expected_relation in test_cases:
            doc = nlp(text)
            
            # Add appropriate entities
            from spacy.tokens import Span
            entities = [
                Span(doc, 0, 1, label="TASK"),      # First word
                Span(doc, 2, 4, label="TIME")       # Time expression
            ]
            doc.ents = entities
            
            result = rel_component(doc)
            relations = result._.relations
            
            # Should find the expected relation
            expected_relations = [r for r in relations if r['relation'] == expected_relation]
            assert len(expected_relations) > 0, f"Expected {expected_relation} not found in: {text}"
    
    @pytest.mark.parametrize("text,entity_pairs,expected_relation", [
        ("Meeting with John", [("TASK", "PERSON")], "WITH_PERSON"),
        ("Call at 2 PM", [("TASK", "TIME")], "AT_TIME"),
        ("Meeting in room A", [("TASK", "LOCATION")], "AT_LOCATION"),
    ])
    def test_specific_relation_extractions(self, nlp, rel_component, text, entity_pairs, expected_relation):
        """Test specific relation extractions with parameterized inputs."""
        doc = nlp(text)
        
        # Add entities based on entity_pairs
        from spacy.tokens import Span
        entities = []
        tokens = text.split()
        
        for i, (label1, label2) in enumerate(entity_pairs):
            if i * 2 < len(tokens):
                entities.append(Span(doc, i * 2, i * 2 + 1, label=label1))
            if i * 2 + 1 < len(tokens):
                entities.append(Span(doc, i * 2 + 1, i * 2 + 2, label=label2))
        
        doc.ents = entities
        
        result = rel_component(doc)
        relations = result._.relations
        
        # Should find the expected relation
        found_relations = [r for r in relations if r['relation'] == expected_relation]
        assert len(found_relations) > 0, f"Expected {expected_relation} not found for: {text}"


if __name__ == "__main__":
    pytest.main([__file__])
