# Setup Instructions

## Prerequisites

- Python 3.8 or higher
- pip package manager

## Installation

### 1. Create Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Download Required Models

```bash
python -m spacy download en_core_web_sm
```

### 4. Create Required Directories

```bash
mkdir -p logs models data/processed data/raw
```

## Verification

Test the installation by running:

```python
import spacy
import dateparser
from sentence_transformers import SentenceTransformer

# Test spaCy
nlp = spacy.load("en_core_web_sm")
doc = nlp("Schedule a meeting tomorrow")
print("spaCy:", [token.text for token in doc])

# Test dateparser
date = dateparser.parse("tomorrow at 3pm")
print("dateparser:", date)

# Test sentence-transformers
model = SentenceTransformer('BAAI/bge-large-en-v1.5')
embeddings = model.encode(["test sentence"])
print("sentence-transformers:", embeddings.shape)
```

## Development Setup

For development work, install additional tools:

```bash
pip install -e .[dev]
```

## Running the Application

### CLI Demo
```bash
python src/app/demo_cli.py "schedule a meeting tomorrow at 3pm"
```

### API Server
```bash
python src/app/scheduler_api.py
```

The API will be available at `http://localhost:8000` with documentation at `http://localhost:8000/docs`.

## Troubleshooting

### spaCy Model Issues
If the spaCy model fails to download:
```bash
python -c "import spacy; spacy.cli.download('en_core_web_sm')"
```

### Import Errors
Ensure virtual environment is activated:
```bash
which python  # Should point to venv/bin/python
```

### Memory Issues
For systems with limited memory, use the smaller model variants in requirements.txt.