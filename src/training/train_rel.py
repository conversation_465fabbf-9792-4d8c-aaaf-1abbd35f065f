"""
SAMPLE CODE - Relation Extraction training script
"""

import spacy
from spacy.training import Example
from spacy.tokens import DocBin
import json
import random
from pathlib import Path
from typing import List, Dict, Tuple, Any
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RelationExtractor:
    """Custom relation extraction component for spaCy."""
    
    def __init__(self, nlp, name="relation_extractor"):
        self.nlp = nlp
        self.name = name
        self.labels = set()
    
    def add_label(self, label: str):
        """Add a relation label."""
        self.labels.add(label)
    
    def __call__(self, doc):
        """Process a document and extract relations."""
        # This is a simplified relation extraction
        # In practice, you'd implement more sophisticated logic
        relations = []
        
        # Extract entities
        entities = [(ent.start, ent.end, ent.label_) for ent in doc.ents]
        
        # Simple rule-based relation extraction
        for i, ent1 in enumerate(entities):
            for j, ent2 in enumerate(entities):
                if i != j:
                    # Example: if PERSON and DATE are close, create SCHEDULED_FOR relation
                    if ent1[2] == "PERSON" and ent2[2] == "DATE":
                        if abs(ent1[0] - ent2[0]) < 10:  # Within 10 tokens
                            relations.append({
                                'head': i,
                                'tail': j,
                                'label': 'SCHEDULED_FOR'
                            })
        
        # Store relations in doc extension
        if not doc.has_extension("relations"):
            doc.set_extension("relations", default=[])
        doc._.relations = relations
        
        return doc


class RelationTrainer:
    """Trainer for Relation Extraction model."""
    
    def __init__(self, model_name: str = "en_core_web_sm"):
        """
        Initialize relation trainer.
        
        Args:
            model_name: Base spaCy model to use
        """
        self.model_name = model_name
        self.nlp = None
    
    def load_training_data(self, data_path: str) -> List[Dict[str, Any]]:
        """
        Load training data from JSON file.
        
        Args:
            data_path: Path to training data file
            
        Returns:
            List of training examples
        """
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        return data
    
    def setup_model(self, labels: List[str]):
        """
        Set up the spaCy model for relation training.
        
        Args:
            labels: List of relation labels to train
        """
        # Load base model
        try:
            self.nlp = spacy.load(self.model_name)
            logger.info(f"Loaded existing model: {self.model_name}")
        except OSError:
            self.nlp = spacy.blank("en")
            logger.info("Created blank English model")
        
        # Add custom relation extractor
        if "relation_extractor" not in self.nlp.pipe_names:
            rel_extractor = RelationExtractor(self.nlp)
            self.nlp.add_pipe(rel_extractor, name="relation_extractor", last=True)
        else:
            rel_extractor = self.nlp.get_pipe("relation_extractor")
        
        # Add labels
        for label in labels:
            rel_extractor.add_label(label)
    
    def create_training_examples(self, data: List[Dict[str, Any]]) -> List[Example]:
        """
        Create spaCy training examples from data.
        
        Args:
            data: List of training data dictionaries
            
        Returns:
            List of spaCy Example objects
        """
        examples = []
        
        for item in data:
            text = item['text']
            doc = self.nlp.make_doc(text)
            
            # Create entity annotations
            entities = []
            for entity in item.get('entities', []):
                entities.append((
                    entity['start'],
                    entity['end'],
                    entity['label']
                ))
            
            # Create relation annotations
            relations = item.get('relations', [])
            
            annotations = {
                'entities': entities,
                'relations': relations
            }
            
            example = Example.from_dict(doc, annotations)
            examples.append(example)
        
        return examples
    
    def train(self, training_data: List[Dict[str, Any]], 
              n_iter: int = 100, output_dir: str = "models/rel"):
        """
        Train the relation extraction model.
        
        Args:
            training_data: List of training examples
            n_iter: Number of training iterations
            output_dir: Directory to save trained model
        """
        # Extract all relation labels
        labels = set()
        for item in training_data:
            for relation in item.get('relations', []):
                labels.add(relation['label'])
        
        self.setup_model(list(labels))
        
        # Create training examples
        examples = self.create_training_examples(training_data)
        
        # Training loop (simplified)
        logger.info(f"Training relation extraction model with {len(examples)} examples")
        
        # In a real implementation, you would implement proper training logic here
        # This is a placeholder for the training process
        
        # Save model
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        self.nlp.to_disk(output_path)
        logger.info(f"Model saved to {output_path}")
    
    def evaluate(self, test_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Evaluate the trained model.
        
        Args:
            test_data: List of test examples
            
        Returns:
            Dictionary with evaluation metrics
        """
        # Simplified evaluation
        correct = 0
        total = 0
        
        for item in test_data:
            text = item['text']
            doc = self.nlp(text)
            
            predicted_relations = getattr(doc._, 'relations', [])
            true_relations = item.get('relations', [])
            
            total += len(true_relations)
            
            # Simple matching (in practice, you'd use more sophisticated metrics)
            for true_rel in true_relations:
                for pred_rel in predicted_relations:
                    if (true_rel['head'] == pred_rel['head'] and 
                        true_rel['tail'] == pred_rel['tail'] and
                        true_rel['label'] == pred_rel['label']):
                        correct += 1
                        break
        
        precision = correct / max(len([r for item in test_data for r in item.get('relations', [])]), 1)
        recall = correct / max(total, 1)
        f1 = 2 * precision * recall / max(precision + recall, 1)
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1
        }


def main():
    """Main training function."""
    # Initialize trainer
    trainer = RelationTrainer()
    
    # Load training data
    training_data = trainer.load_training_data("data/examples/sample_requests.json")
    logger.info(f"Loaded {len(training_data)} training examples")
    
    # Split data (80% train, 20% test)
    split_idx = int(0.8 * len(training_data))
    train_data = training_data[:split_idx]
    test_data = training_data[split_idx:]
    
    # Train model
    trainer.train(train_data, n_iter=50)
    
    # Evaluate model
    if test_data:
        scores = trainer.evaluate(test_data)
        logger.info(f"Evaluation scores: {scores}")


if __name__ == "__main__":
    main()
