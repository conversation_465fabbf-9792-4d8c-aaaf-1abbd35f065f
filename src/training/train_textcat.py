"""
SAMPLE CODE - Text Classification training script
"""

import spacy
from spacy.training import Example
import json
import random
from pathlib import Path
from typing import List, Dict, Tuple, Any
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextCatTrainer:
    """Trainer for Text Classification model."""
    
    def __init__(self, model_name: str = "en_core_web_sm"):
        """
        Initialize text classification trainer.
        
        Args:
            model_name: Base spaCy model to use
        """
        self.model_name = model_name
        self.nlp = None
        self.textcat = None
    
    def load_training_data(self, data_path: str) -> List[Tuple[str, Dict[str, bool]]]:
        """
        Load training data from JSON file.
        
        Args:
            data_path: Path to training data file
            
        Returns:
            List of (text, categories) tuples
        """
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        training_data = []
        for item in data:
            text = item['text']
            category = item.get('category', 'OTHER')
            
            # Create one-hot encoding for categories
            categories = {
                'MEETING': category == 'MEETING',
                'REMINDER': category == 'REMINDER',
                'DEADLINE': category == 'DEADLINE',
                'RECURRING': category == 'RECURRING',
                'CANCELLATION': category == 'CANCELLATION',
                'OTHER': category == 'OTHER'
            }
            
            training_data.append((text, {'cats': categories}))
        
        return training_data
    
    def setup_model(self, labels: List[str]):
        """
        Set up the spaCy model for text classification.
        
        Args:
            labels: List of category labels to train
        """
        # Load base model or create blank
        try:
            self.nlp = spacy.load(self.model_name)
            logger.info(f"Loaded existing model: {self.model_name}")
        except OSError:
            self.nlp = spacy.blank("en")
            logger.info("Created blank English model")
        
        # Add textcat pipe if not present
        if "textcat" not in self.nlp.pipe_names:
            self.textcat = self.nlp.add_pipe("textcat", last=True)
        else:
            self.textcat = self.nlp.get_pipe("textcat")
        
        # Add labels to textcat
        for label in labels:
            self.textcat.add_label(label)
    
    def train(self, training_data: List[Tuple[str, Dict[str, Any]]], 
              n_iter: int = 100, output_dir: str = "models/textcat"):
        """
        Train the text classification model.
        
        Args:
            training_data: List of (text, annotations) tuples
            n_iter: Number of training iterations
            output_dir: Directory to save trained model
        """
        # Extract all labels from training data
        labels = set()
        for text, annotations in training_data:
            for label in annotations['cats'].keys():
                labels.add(label)
        
        self.setup_model(list(labels))
        
        # Get names of other pipes to disable during training
        pipe_exceptions = ["textcat", "trf_wordpiecer", "trf_tok2vec"]
        other_pipes = [pipe for pipe in self.nlp.pipe_names 
                      if pipe not in pipe_exceptions]
        
        # Training loop
        with self.nlp.disable_pipes(*other_pipes):
            optimizer = self.nlp.begin_training()
            
            for iteration in range(n_iter):
                random.shuffle(training_data)
                losses = {}
                
                # Create examples
                examples = []
                for text, annotations in training_data:
                    doc = self.nlp.make_doc(text)
                    example = Example.from_dict(doc, annotations)
                    examples.append(example)
                
                # Update model
                self.nlp.update(examples, drop=0.5, losses=losses, sgd=optimizer)
                
                if iteration % 10 == 0:
                    logger.info(f"Iteration {iteration}, Losses: {losses}")
        
        # Save model
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        self.nlp.to_disk(output_path)
        logger.info(f"Model saved to {output_path}")
    
    def evaluate(self, test_data: List[Tuple[str, Dict[str, Any]]]) -> Dict[str, float]:
        """
        Evaluate the trained model.
        
        Args:
            test_data: List of (text, annotations) tuples for testing
            
        Returns:
            Dictionary with evaluation metrics
        """
        examples = []
        for text, annotations in test_data:
            doc = self.nlp.make_doc(text)
            example = Example.from_dict(doc, annotations)
            examples.append(example)
        
        scores = self.nlp.evaluate(examples)
        return scores
    
    def predict(self, text: str) -> Dict[str, float]:
        """
        Predict category for a given text.
        
        Args:
            text: Input text to classify
            
        Returns:
            Dictionary with category probabilities
        """
        doc = self.nlp(text)
        return doc.cats


def main():
    """Main training function."""
    # Initialize trainer
    trainer = TextCatTrainer()
    
    # Load training data
    training_data = trainer.load_training_data("data/examples/sample_requests.json")
    logger.info(f"Loaded {len(training_data)} training examples")
    
    # Split data (80% train, 20% test)
    split_idx = int(0.8 * len(training_data))
    train_data = training_data[:split_idx]
    test_data = training_data[split_idx:]
    
    # Train model
    trainer.train(train_data, n_iter=50)
    
    # Evaluate model
    if test_data:
        scores = trainer.evaluate(test_data)
        logger.info(f"Evaluation scores: {scores}")
    
    # Test prediction
    sample_text = "Schedule a meeting with the team next Monday"
    predictions = trainer.predict(sample_text)
    logger.info(f"Predictions for '{sample_text}': {predictions}")


if __name__ == "__main__":
    main()
