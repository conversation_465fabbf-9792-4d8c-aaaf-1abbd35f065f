"""
SAMPLE CODE - Named Entity Recognition training script
"""

import spacy
from spacy.training import Example
from spacy.util import minibatch, compounding
import json
import random
from pathlib import Path
from typing import List, Dict, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NERTrainer:
    """Trainer for Named Entity Recognition model."""
    
    def __init__(self, model_name: str = "en_core_web_sm"):
        """
        Initialize NER trainer.
        
        Args:
            model_name: Base spaCy model to use
        """
        self.model_name = model_name
        self.nlp = None
        self.ner = None
    
    def load_training_data(self, data_path: str) -> List[Tuple[str, Dict]]:
        """
        Load training data from JSON file.
        
        Args:
            data_path: Path to training data file
            
        Returns:
            List of (text, annotations) tuples
        """
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        training_data = []
        for item in data:
            text = item['text']
            entities = []
            
            for entity in item.get('entities', []):
                entities.append((
                    entity['start'],
                    entity['end'],
                    entity['label']
                ))
            
            annotations = {'entities': entities}
            training_data.append((text, annotations))
        
        return training_data
    
    def setup_model(self, labels: List[str]):
        """
        Set up the spaCy model for training.
        
        Args:
            labels: List of entity labels to train
        """
        # Load base model or create blank
        try:
            self.nlp = spacy.load(self.model_name)
            logger.info(f"Loaded existing model: {self.model_name}")
        except OSError:
            self.nlp = spacy.blank("en")
            logger.info("Created blank English model")
        
        # Add NER pipe if not present
        if "ner" not in self.nlp.pipe_names:
            self.ner = self.nlp.add_pipe("ner", last=True)
        else:
            self.ner = self.nlp.get_pipe("ner")
        
        # Add labels to NER
        for label in labels:
            self.ner.add_label(label)
    
    def train(self, training_data: List[Tuple[str, Dict]], 
              n_iter: int = 100, output_dir: str = "models/ner"):
        """
        Train the NER model.
        
        Args:
            training_data: List of (text, annotations) tuples
            n_iter: Number of training iterations
            output_dir: Directory to save trained model
        """
        # Extract all labels from training data
        labels = set()
        for text, annotations in training_data:
            for entity in annotations['entities']:
                labels.add(entity[2])
        
        self.setup_model(list(labels))
        
        # Get names of other pipes to disable during training
        pipe_exceptions = ["ner", "trf_wordpiecer", "trf_tok2vec"]
        other_pipes = [pipe for pipe in self.nlp.pipe_names 
                      if pipe not in pipe_exceptions]
        
        # Training loop
        with self.nlp.disable_pipes(*other_pipes):
            optimizer = self.nlp.begin_training()
            
            for iteration in range(n_iter):
                random.shuffle(training_data)
                losses = {}
                
                # Create batches
                batches = minibatch(training_data, size=compounding(4.0, 32.0, 1.001))
                
                for batch in batches:
                    examples = []
                    for text, annotations in batch:
                        doc = self.nlp.make_doc(text)
                        example = Example.from_dict(doc, annotations)
                        examples.append(example)
                    
                    self.nlp.update(examples, drop=0.5, losses=losses, sgd=optimizer)
                
                if iteration % 10 == 0:
                    logger.info(f"Iteration {iteration}, Losses: {losses}")
        
        # Save model
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        self.nlp.to_disk(output_path)
        logger.info(f"Model saved to {output_path}")
    
    def evaluate(self, test_data: List[Tuple[str, Dict]]) -> Dict[str, float]:
        """
        Evaluate the trained model.
        
        Args:
            test_data: List of (text, annotations) tuples for testing
            
        Returns:
            Dictionary with evaluation metrics
        """
        examples = []
        for text, annotations in test_data:
            doc = self.nlp.make_doc(text)
            example = Example.from_dict(doc, annotations)
            examples.append(example)
        
        scores = self.nlp.evaluate(examples)
        return scores


def main():
    """Main training function."""
    # Initialize trainer
    trainer = NERTrainer()
    
    # Load training data
    training_data = trainer.load_training_data("data/examples/sample_requests.json")
    logger.info(f"Loaded {len(training_data)} training examples")
    
    # Split data (80% train, 20% test)
    split_idx = int(0.8 * len(training_data))
    train_data = training_data[:split_idx]
    test_data = training_data[split_idx:]
    
    # Train model
    trainer.train(train_data, n_iter=50)
    
    # Evaluate model
    if test_data:
        scores = trainer.evaluate(test_data)
        logger.info(f"Evaluation scores: {scores}")


if __name__ == "__main__":
    main()
