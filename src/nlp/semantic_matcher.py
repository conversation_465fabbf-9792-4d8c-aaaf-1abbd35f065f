"""
Semantic text matching using sentence-transformers.

Provides semantic similarity matching for task comparison.
"""

from typing import List, Tu<PERSON>, Dict, Any, Optional
import numpy as np
import logging
from dataclasses import dataclass

# Optional imports - gracefully handle if not available
try:
    from sentence_transformers import SentenceTransformer
    from sklearn.metrics.pairwise import cosine_similarity
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

# Fallback to basic fuzzy matching if sentence-transformers not available
try:
    from rapidfuzz import fuzz
    RAPIDFUZZ_AVAILABLE = True
except ImportError:
    RAPIDFUZZ_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class MatchResult:
    """Result of semantic matching operation."""
    matched_text: str
    similarity_score: float
    index: int
    method: str  # 'semantic' or 'fuzzy'


class SemanticMatcher:
    """
    Semantic text matcher using sentence-transformers.
    
    Uses the BAAI/bge-large-en-v1.5 model for high-accuracy semantic similarity.
    """
    
    def __init__(self, model_name: str = 'BAAI/bge-large-en-v1.5'):
        """
        Initialize semantic matcher.
        
        Args:
            model_name: sentence-transformers model name
        """
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError(
                "sentence-transformers not available. "
                "Install with: pip install sentence-transformers"
            )
        
        self.model_name = model_name
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """Load the sentence transformer model."""
        try:
            logger.info(f"Loading sentence-transformers model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            logger.info("Model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load model {self.model_name}: {e}")
            raise
    
    def encode_texts(self, texts: List[str]) -> np.ndarray:
        """
        Encode texts into sentence embeddings.
        
        Args:
            texts: List of text strings
            
        Returns:
            Numpy array of embeddings
        """
        if not texts:
            return np.array([])
        
        try:
            embeddings = self.model.encode(texts, convert_to_numpy=True)
            logger.debug(f"Encoded {len(texts)} texts into embeddings")
            return embeddings
        except Exception as e:
            logger.error(f"Error encoding texts: {e}")
            raise
    
    def find_best_match(
        self, 
        query: str, 
        candidates: List[str], 
        threshold: float = 0.7
    ) -> Optional[MatchResult]:
        """
        Find the best semantic match for a query among candidates.
        
        Args:
            query: Text to match against
            candidates: List of candidate texts
            threshold: Minimum similarity threshold
            
        Returns:
            MatchResult if good match found, None otherwise
            
        Examples:
            >>> matcher = SemanticMatcher()
            >>> candidates = ["team meeting", "call with client", "lunch break"]
            >>> result = matcher.find_best_match("meeting with team", candidates)
            >>> result.matched_text
            "team meeting"
            >>> result.similarity_score
            0.85
        """
        if not candidates:
            return None
        
        try:
            # Encode query and candidates
            all_texts = [query] + candidates
            embeddings = self.encode_texts(all_texts)
            
            query_embedding = embeddings[0:1]  # First embedding
            candidate_embeddings = embeddings[1:]  # Rest of embeddings
            
            # Calculate cosine similarities
            similarities = cosine_similarity(query_embedding, candidate_embeddings)[0]
            
            # Find best match
            best_idx = np.argmax(similarities)
            best_score = similarities[best_idx]
            
            if best_score >= threshold:
                return MatchResult(
                    matched_text=candidates[best_idx],
                    similarity_score=float(best_score),
                    index=best_idx,
                    method='semantic'
                )
            
            logger.debug(f"Best match score {best_score:.3f} below threshold {threshold}")
            return None
            
        except Exception as e:
            logger.error(f"Error in semantic matching: {e}")
            return None
    
    def find_top_matches(
        self, 
        query: str, 
        candidates: List[str], 
        top_k: int = 3,
        threshold: float = 0.5
    ) -> List[MatchResult]:
        """
        Find top K semantic matches for a query.
        
        Args:
            query: Text to match against
            candidates: List of candidate texts
            top_k: Number of top matches to return
            threshold: Minimum similarity threshold
            
        Returns:
            List of MatchResult objects, sorted by similarity (descending)
        """
        if not candidates:
            return []
        
        try:
            # Encode query and candidates
            all_texts = [query] + candidates
            embeddings = self.encode_texts(all_texts)
            
            query_embedding = embeddings[0:1]
            candidate_embeddings = embeddings[1:]
            
            # Calculate similarities
            similarities = cosine_similarity(query_embedding, candidate_embeddings)[0]
            
            # Get top K matches above threshold
            top_indices = np.argsort(similarities)[::-1][:top_k]
            
            results = []
            for idx in top_indices:
                score = similarities[idx]
                if score >= threshold:
                    results.append(MatchResult(
                        matched_text=candidates[idx],
                        similarity_score=float(score),
                        index=int(idx),
                        method='semantic'
                    ))
            
            return results
            
        except Exception as e:
            logger.error(f"Error in top-K matching: {e}")
            return []
    
    def batch_similarity(
        self, 
        queries: List[str], 
        candidates: List[str]
    ) -> np.ndarray:
        """
        Compute similarity matrix between all queries and candidates.
        
        Args:
            queries: List of query texts
            candidates: List of candidate texts
            
        Returns:
            Similarity matrix (queries x candidates)
        """
        try:
            query_embeddings = self.encode_texts(queries)
            candidate_embeddings = self.encode_texts(candidates)
            
            return cosine_similarity(query_embeddings, candidate_embeddings)
            
        except Exception as e:
            logger.error(f"Error in batch similarity: {e}")
            return np.array([])


class FuzzyMatcher:
    """
    Fallback fuzzy string matcher using rapidfuzz.
    
    Used when semantic matching is not available or as a backup.
    """
    
    def __init__(self):
        """Initialize fuzzy matcher."""
        if not RAPIDFUZZ_AVAILABLE:
            raise ImportError(
                "rapidfuzz not available. "
                "Install with: pip install rapidfuzz"
            )
    
    def find_best_match(
        self, 
        query: str, 
        candidates: List[str], 
        threshold: float = 70.0
    ) -> Optional[MatchResult]:
        """
        Find best fuzzy match for query among candidates.
        
        Args:
            query: Text to match against
            candidates: List of candidate texts
            threshold: Minimum fuzzy score threshold (0-100)
            
        Returns:
            MatchResult if good match found, None otherwise
        """
        if not candidates:
            return None
        
        best_score = 0.0
        best_idx = -1
        
        for i, candidate in enumerate(candidates):
            score = fuzz.ratio(query.lower(), candidate.lower())
            if score > best_score:
                best_score = score
                best_idx = i
        
        if best_score >= threshold:
            return MatchResult(
                matched_text=candidates[best_idx],
                similarity_score=best_score / 100.0,  # Normalize to 0-1
                index=best_idx,
                method='fuzzy'
            )
        
        return None


class HybridMatcher:
    """
    Hybrid matcher combining semantic and fuzzy matching.
    
    Tries semantic matching first, falls back to fuzzy matching.
    """
    
    def __init__(self, model_name: str = 'BAAI/bge-large-en-v1.5'):
        """
        Initialize hybrid matcher.
        
        Args:
            model_name: sentence-transformers model name
        """
        self.semantic_matcher = None
        self.fuzzy_matcher = None
        
        # Try to initialize semantic matcher
        if TRANSFORMERS_AVAILABLE:
            try:
                self.semantic_matcher = SemanticMatcher(model_name)
                logger.info("Hybrid matcher initialized with semantic matching")
            except Exception as e:
                logger.warning(f"Could not initialize semantic matcher: {e}")
        
        # Initialize fuzzy matcher as backup
        if RAPIDFUZZ_AVAILABLE:
            try:
                self.fuzzy_matcher = FuzzyMatcher()
                logger.info("Fuzzy matching available as backup")
            except Exception as e:
                logger.warning(f"Could not initialize fuzzy matcher: {e}")
        
        if not self.semantic_matcher and not self.fuzzy_matcher:
            raise ImportError("No matching libraries available")
    
    def find_best_match(
        self, 
        query: str, 
        candidates: List[str], 
        semantic_threshold: float = 0.7,
        fuzzy_threshold: float = 70.0
    ) -> Optional[MatchResult]:
        """
        Find best match using hybrid approach.
        
        Args:
            query: Text to match against
            candidates: List of candidate texts
            semantic_threshold: Threshold for semantic matching
            fuzzy_threshold: Threshold for fuzzy matching
            
        Returns:
            MatchResult if good match found, None otherwise
        """
        # Try semantic matching first
        if self.semantic_matcher:
            try:
                result = self.semantic_matcher.find_best_match(
                    query, candidates, semantic_threshold
                )
                if result:
                    return result
            except Exception as e:
                logger.warning(f"Semantic matching failed: {e}")
        
        # Fall back to fuzzy matching
        if self.fuzzy_matcher:
            try:
                result = self.fuzzy_matcher.find_best_match(
                    query, candidates, fuzzy_threshold
                )
                if result:
                    result.method = 'hybrid_fuzzy_fallback'
                    return result
            except Exception as e:
                logger.warning(f"Fuzzy matching failed: {e}")
        
        return None


# Factory function
def create_matcher(
    matcher_type: str = "hybrid",
    model_name: str = 'BAAI/bge-large-en-v1.5'
) -> Union[SemanticMatcher, FuzzyMatcher, HybridMatcher]:
    """
    Factory function to create text matchers.
    
    Args:
        matcher_type: 'semantic', 'fuzzy', or 'hybrid'
        model_name: Model name for semantic matching
        
    Returns:
        Initialized matcher instance
    """
    if matcher_type == "semantic":
        return SemanticMatcher(model_name)
    elif matcher_type == "fuzzy":
        return FuzzyMatcher()
    elif matcher_type == "hybrid":
        return HybridMatcher(model_name)
    else:
        raise ValueError(f"Unknown matcher type: {matcher_type}")


# Convenience functions
def find_similar_task(
    query: str, 
    existing_tasks: List[str], 
    threshold: float = 0.7
) -> Optional[MatchResult]:
    """Simple task similarity function."""
    matcher = create_matcher("hybrid")
    return matcher.find_best_match(query, existing_tasks, threshold)