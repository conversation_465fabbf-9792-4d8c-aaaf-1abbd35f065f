"""
Intent classification for scheduler commands.

Supports both rule-based and machine learning approaches.
"""

from typing import Dict, List, Optional, Union, Tuple
import re
import logging
from enum import Enum
from dataclasses import dataclass

# Optional ML imports
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.linear_model import LogisticRegression
    from sklearn.pipeline import Pipeline
    import joblib
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

logger = logging.getLogger(__name__)


class IntentType(Enum):
    """Supported intent types for scheduler commands."""
    ADD = "add"
    MODIFY = "modify" 
    CANCEL = "cancel"
    QUERY = "query"
    UNKNOWN = "unknown"


@dataclass
class IntentResult:
    """Result of intent classification."""
    intent: IntentType
    confidence: float
    matched_keywords: List[str]
    method: str  # 'rule_based' or 'ml_model'


class RuleBasedIntentClassifier:
    """
    Rule-based intent classifier using keyword matching.
    
    Fast and reliable for MVP implementation.
    """
    
    def __init__(self):
        """Initialize with predefined keyword patterns."""
        self.intent_keywords = {
            IntentType.ADD: [
                'add', 'create', 'schedule', 'new', 'set', 'book', 'plan',
                'arrange', 'make', 'setup', 'organize', 'put'
            ],
            IntentType.MODIFY: [
                'change', 'update', 'edit', 'alter', 'modify', 'move', 
                'reschedule', 'shift', 'adjust', 'fix'
            ],
            IntentType.CANCEL: [
                'delete', 'remove', 'cancel', 'forget', 'drop', 'clear',
                'eliminate', 'abort', 'dismiss'
            ],
            IntentType.QUERY: [
                'what', 'when', 'where', 'who', 'how', 'show', 'list',
                'check', 'find', 'search', 'get', 'tell', 'display'
            ]
        }
        
        # Compile regex patterns for better performance
        self.patterns = {}
        for intent, keywords in self.intent_keywords.items():
            pattern = r'\b(' + '|'.join(keywords) + r')\b'
            self.patterns[intent] = re.compile(pattern, re.IGNORECASE)
    
    def classify(self, text: str) -> IntentResult:
        """
        Classify intent using rule-based keyword matching.
        
        Args:
            text: Input command text
            
        Returns:
            IntentResult with classification details
        """
        text = text.lower().strip()
        matches = {}
        
        # Find matches for each intent
        for intent, pattern in self.patterns.items():
            found_matches = pattern.findall(text)
            if found_matches:
                matches[intent] = found_matches
        
        if not matches:
            return IntentResult(
                intent=IntentType.UNKNOWN,
                confidence=0.0,
                matched_keywords=[],
                method='rule_based'
            )
        
        # Select intent with most matches (simple scoring)
        best_intent = max(matches.keys(), key=lambda k: len(matches[k]))
        matched_words = matches[best_intent]
        
        # Calculate simple confidence based on number of matches
        confidence = min(len(matched_words) * 0.3, 1.0)
        
        return IntentResult(
            intent=best_intent,
            confidence=confidence,
            matched_keywords=matched_words,
            method='rule_based'
        )
    
    def add_keywords(self, intent: IntentType, keywords: List[str]):
        """Add new keywords for an intent type."""
        self.intent_keywords[intent].extend(keywords)
        # Recompile pattern
        pattern = r'\b(' + '|'.join(self.intent_keywords[intent]) + r')\b'
        self.patterns[intent] = re.compile(pattern, re.IGNORECASE)


class MLIntentClassifier:
    """
    Machine learning intent classifier using scikit-learn.
    
    For future enhancement beyond MVP.
    """
    
    def __init__(self):
        """Initialize ML classifier."""
        if not ML_AVAILABLE:
            raise ImportError("scikit-learn not available. Install with: pip install scikit-learn")
        
        # Create sklearn pipeline
        self.pipeline = Pipeline([
            ('tfidf', TfidfVectorizer(
                lowercase=True,
                stop_words='english',
                ngram_range=(1, 2),
                max_features=1000
            )),
            ('classifier', LogisticRegression(random_state=42))
        ])
        
        self.is_trained = False
        self.label_map = {
            'add': IntentType.ADD,
            'modify': IntentType.MODIFY,
            'cancel': IntentType.CANCEL,
            'query': IntentType.QUERY
        }

    def train(self, texts: List[str], labels: List[str]):
        """
        Train the ML model on labeled data.
        
        Args:
            texts: Training text samples
            labels: Corresponding intent labels
        """
        logger.info(f"Training ML intent classifier on {len(texts)} samples")
        self.pipeline.fit(texts, labels)
        self.is_trained = True
        logger.info("Training completed")
    
    def classify(self, text: str) -> IntentResult:
        """
        Classify intent using trained ML model.
        
        Args:
            text: Input command text
            
        Returns:
            IntentResult with classification details
        """
        if not self.is_trained:
            raise ValueError("Model must be trained before classification")
        
        # Get prediction and probability
        prediction = self.pipeline.predict([text])[0]
        probabilities = self.pipeline.predict_proba([text])[0]
        
        # Get confidence score
        max_prob_idx = probabilities.argmax()
        confidence = probabilities[max_prob_idx]
        
        # Map string label to enum
        intent = self.label_map.get(prediction, IntentType.UNKNOWN)
        
        return IntentResult(
            intent=intent,
            confidence=confidence,
            matched_keywords=[],  # Not applicable for ML model
            method='ml_model'
        )
    
    def save_model(self, filepath: str):
        """Save trained model to disk."""
        if not self.is_trained:
            raise ValueError("Cannot save untrained model")
        joblib.dump(self.pipeline, filepath)
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load trained model from disk."""
        self.pipeline = joblib.load(filepath)
        self.is_trained = True
        logger.info(f"Model loaded from {filepath}")


class HybridIntentClassifier:
    """
    Hybrid classifier combining rules and ML as suggested in research.
    
    Uses rules first, falls back to ML if no rule matches.
    """
    
    def __init__(self, ml_model_path: Optional[str] = None):
        """
        Initialize hybrid classifier.
        
        Args:
            ml_model_path: Path to trained ML model (optional)
        """
        self.rule_classifier = RuleBasedIntentClassifier()
        self.ml_classifier = None
        
        if ml_model_path and ML_AVAILABLE:
            try:
                self.ml_classifier = MLIntentClassifier()
                self.ml_classifier.load_model(ml_model_path)
                logger.info("Hybrid classifier initialized with ML fallback")
            except Exception as e:
                logger.warning(f"Could not load ML model: {e}")
                logger.info("Using rule-based classifier only")
    
    def classify(self, text: str) -> IntentResult:
        """
        Classify using hybrid approach: rules first, then ML.
        
        Args:
            text: Input command text
            
        Returns:
            IntentResult with classification details
        """
        # Try rule-based first
        rule_result = self.rule_classifier.classify(text)
        
        # If rule-based gives good confidence, use it
        if rule_result.confidence >= 0.3 or rule_result.intent != IntentType.UNKNOWN:
            return rule_result
        
        # Fall back to ML if available
        if self.ml_classifier:
            try:
                ml_result = self.ml_classifier.classify(text)
                ml_result.method = 'hybrid_ml_fallback'
                return ml_result
            except Exception as e:
                logger.warning(f"ML fallback failed: {e}")
        
        # Return rule result if ML not available or failed
        return rule_result


# Factory function for easy initialization
def create_intent_classifier(
    classifier_type: str = "rule_based",
    ml_model_path: Optional[str] = None
) -> Union[RuleBasedIntentClassifier, MLIntentClassifier, HybridIntentClassifier]:
    """
    Factory function to create intent classifiers.
    
    Args:
        classifier_type: 'rule_based', 'ml', or 'hybrid'
        ml_model_path: Path to ML model (for ml/hybrid types)
        
    Returns:
        Initialized classifier instance
    """
    if classifier_type == "rule_based":
        return RuleBasedIntentClassifier()
    elif classifier_type == "ml":
        if not ML_AVAILABLE:
            raise ImportError("ML classifier requires scikit-learn")
        return MLIntentClassifier()
    elif classifier_type == "hybrid":
        return HybridIntentClassifier(ml_model_path)
    else:
        raise ValueError(f"Unknown classifier type: {classifier_type}")


# Convenience function
def classify_intent(text: str, classifier_type: str = "rule_based") -> IntentResult:
    """Simple intent classification function."""
    classifier = create_intent_classifier(classifier_type)
    return classifier.classify(text)