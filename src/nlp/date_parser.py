"""
Date and time parsing module using dateparser.

This module provides robust natural language date/time parsing.
"""

from datetime import datetime
from typing import Optional, Dict, Any
import dateparser
from dateparser.search import search_dates
import logging

logger = logging.getLogger(__name__)


class DateTimeParser:
    """
    Natural language date/time parser using dateparser library.
    
    Handles expressions like:
    - "next Monday at 3 PM"
    - "tomorrow morning"
    - "in 2 hours"
    - "Friday at 5:30"
    """
    
    def __init__(self, settings: Optional[Dict[str, Any]] = None):
        """
        Initialize the date parser with optional settings.
        
        Args:
            settings: dateparser settings dict
        """
        self.default_settings = {
            'PREFER_DAY_OF_MONTH': 'first',
            'PREFER_DATES_FROM': 'future',
            'RETURN_AS_TIMEZONE_AWARE': False,
            'TO_TIMEZONE': None,
            'RELATIVE_BASE': None
        }
        
        if settings:
            self.default_settings.update(settings)
    
    def parse_datetime(
        self, 
        text: str, 
        settings: Optional[Dict[str, Any]] = None
    ) -> Optional[datetime]:
        """
        Parse a single datetime from text.
        
        Args:
            text: Natural language date/time expression
            settings: Override default settings
            
        Returns:
            Parsed datetime object or None if parsing fails
            
        Examples:
            >>> parser = DateTimeParser()
            >>> parser.parse_datetime("next Monday at 3 PM")
            datetime(2024, 1, 15, 15, 0)
        """
        parse_settings = self.default_settings.copy()
        if settings:
            parse_settings.update(settings)
            
        try:
            result = dateparser.parse(text, settings=parse_settings)
            if result:
                logger.debug(f"Parsed '{text}' -> {result}")
            else:
                logger.warning(f"Failed to parse date from: '{text}'")
            return result
        except Exception as e:
            logger.error(f"Error parsing datetime '{text}': {e}")
            return None
    
    def find_dates_in_text(
        self, 
        text: str, 
        settings: Optional[Dict[str, Any]] = None
    ) -> list:
        """
        Find all date expressions in a text string.
        
        Args:
            text: Text containing potential date expressions
            settings: Override default settings
            
        Returns:
            List of tuples (date_string, datetime_object)
            
        Examples:
            >>> parser = DateTimeParser()
            >>> parser.find_dates_in_text("Meet John on Monday and call Sarah next Friday")
            [('Monday', datetime(...)), ('next Friday', datetime(...))]
        """
        parse_settings = self.default_settings.copy()
        if settings:
            parse_settings.update(settings)
            
        try:
            results = search_dates(text, settings=parse_settings)
            if results:
                logger.debug(f"Found {len(results)} dates in: '{text}'")
                return results
            else:
                logger.debug(f"No dates found in: '{text}'")
                return []
        except Exception as e:
            logger.error(f"Error finding dates in '{text}': {e}")
            return []
    
    def extract_time_info(self, text: str) -> Dict[str, Any]:
        """
        Extract comprehensive time information from text.
        
        Args:
            text: Natural language text
            
        Returns:
            Dictionary containing:
            - parsed_datetime: Main datetime if found
            - all_dates: List of all found dates
            - has_time: Whether time information was specified
            - is_relative: Whether expression is relative (e.g., "in 2 hours")
        """
        main_datetime = self.parse_datetime(text)
        all_dates = self.find_dates_in_text(text)
        
        # Check if time was explicitly mentioned
        time_indicators = ['am', 'pm', ':', 'hour', 'minute', 'o\'clock', 'noon', 'midnight']
        has_time = any(indicator in text.lower() for indicator in time_indicators)
        
        # Check if expression is relative
        relative_indicators = ['in', 'after', 'from now', 'ago', 'next', 'last', 'tomorrow', 'yesterday']
        is_relative = any(indicator in text.lower() for indicator in relative_indicators)
        
        return {
            'parsed_datetime': main_datetime,
            'all_dates': all_dates,
            'has_time': has_time,
            'is_relative': is_relative,
            'original_text': text
        }


# Convenience functions for backward compatibility
def parse_date(text: str) -> Optional[datetime]:
    """Simple date parsing function."""
    parser = DateTimeParser()
    return parser.parse_datetime(text)


def find_dates(text: str) -> list:
    """Simple date finding function."""
    parser = DateTimeParser()
    return parser.find_dates_in_text(text)