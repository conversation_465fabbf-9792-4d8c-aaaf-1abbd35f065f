"""
SAMPLE CODE - Named Entity Recognition pipeline component
"""

import spacy
from spacy.tokens import Doc, Span
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class SchedulerNERComponent:
    """Custom NER component for scheduler-specific entities."""
    
    def __init__(self, nlp, name="scheduler_ner", model_path: Optional[str] = None):
        """
        Initialize the NER component.
        
        Args:
            nlp: spaCy language model
            name: Component name
            model_path: Path to trained NER model
        """
        self.nlp = nlp
        self.name = name
        self.model_path = model_path
        
        # Load trained model if provided
        if model_path:
            self.ner_model = spacy.load(model_path)
        else:
            self.ner_model = None
        
        # Define scheduler-specific entity types
        self.entity_types = {
            'PERSON': 'People mentioned in scheduling requests',
            'DATE': 'Dates and date expressions',
            'TIME': 'Times and time expressions',
            'LOCATION': 'Meeting locations and venues',
            'TASK': 'Tasks, meetings, and activities',
            'DURATION': 'Duration expressions',
            'FREQUENCY': 'Recurring patterns'
        }
    
    def __call__(self, doc: Doc) -> Doc:
        """
        Process a document and extract scheduler-specific entities.
        
        Args:
            doc: spaCy Doc object
            
        Returns:
            Doc with extracted entities
        """
        if self.ner_model:
            # Use trained model
            ner_doc = self.ner_model(doc.text)
            
            # Transfer entities to original doc
            entities = []
            for ent in ner_doc.ents:
                if ent.label_ in self.entity_types:
                    span = doc.char_span(ent.start_char, ent.end_char, label=ent.label_)
                    if span:
                        entities.append(span)
            
            doc.ents = entities
        else:
            # Use rule-based extraction as fallback
            doc = self._rule_based_extraction(doc)
        
        return doc
    
    def _rule_based_extraction(self, doc: Doc) -> Doc:
        """
        Rule-based entity extraction for common scheduler entities.
        
        Args:
            doc: spaCy Doc object
            
        Returns:
            Doc with extracted entities
        """
        entities = []
        
        # Extract existing entities from base model
        for ent in doc.ents:
            if ent.label_ in ['PERSON', 'DATE', 'TIME', 'ORG', 'GPE']:
                entities.append(ent)
        
        # Add custom rule-based extractions
        entities.extend(self._extract_time_expressions(doc))
        entities.extend(self._extract_task_expressions(doc))
        entities.extend(self._extract_location_expressions(doc))
        
        # Remove overlapping entities
        entities = self._remove_overlaps(entities)
        
        doc.ents = entities
        return doc
    
    def _extract_time_expressions(self, doc: Doc) -> List[Span]:
        """Extract time-related expressions."""
        time_entities = []
        
        # Common time patterns
        time_patterns = [
            r'\b\d{1,2}:\d{2}\s*(AM|PM|am|pm)?\b',
            r'\b\d{1,2}\s*(AM|PM|am|pm)\b',
            r'\bmidnight\b',
            r'\bnoon\b',
            r'\bmorning\b',
            r'\bafternoon\b',
            r'\bevening\b',
            r'\bnight\b'
        ]
        
        import re
        for pattern in time_patterns:
            for match in re.finditer(pattern, doc.text, re.IGNORECASE):
                span = doc.char_span(match.start(), match.end(), label="TIME")
                if span:
                    time_entities.append(span)
        
        return time_entities
    
    def _extract_task_expressions(self, doc: Doc) -> List[Span]:
        """Extract task and activity expressions."""
        task_entities = []
        
        # Common task keywords
        task_keywords = [
            'meeting', 'call', 'appointment', 'interview', 'presentation',
            'conference', 'workshop', 'training', 'review', 'standup',
            'demo', 'lunch', 'dinner', 'break', 'session'
        ]
        
        for token in doc:
            if token.lemma_.lower() in task_keywords:
                # Extend to include modifiers
                start = token.i
                end = token.i + 1
                
                # Look for preceding adjectives/determiners
                while start > 0 and doc[start-1].pos_ in ['DET', 'ADJ']:
                    start -= 1
                
                # Look for following nouns/adjectives
                while end < len(doc) and doc[end].pos_ in ['NOUN', 'ADJ']:
                    end += 1
                
                span = doc[start:end]
                task_entities.append(Span(doc, start, end, label="TASK"))
        
        return task_entities
    
    def _extract_location_expressions(self, doc: Doc) -> List[Span]:
        """Extract location expressions."""
        location_entities = []
        
        # Common location keywords
        location_keywords = [
            'room', 'office', 'building', 'floor', 'conference room',
            'meeting room', 'boardroom', 'auditorium', 'hall', 'venue'
        ]
        
        for token in doc:
            if any(keyword in token.text.lower() for keyword in location_keywords):
                # Create span for location
                start = max(0, token.i - 1)  # Include preceding word
                end = min(len(doc), token.i + 2)  # Include following word
                
                span = doc[start:end]
                location_entities.append(Span(doc, start, end, label="LOCATION"))
        
        return location_entities
    
    def _remove_overlaps(self, entities: List[Span]) -> List[Span]:
        """Remove overlapping entity spans."""
        # Sort by start position
        entities = sorted(entities, key=lambda x: (x.start, -x.end))
        
        non_overlapping = []
        for entity in entities:
            # Check if this entity overlaps with any already added
            overlaps = False
            for existing in non_overlapping:
                if (entity.start < existing.end and entity.end > existing.start):
                    overlaps = True
                    break
            
            if not overlaps:
                non_overlapping.append(entity)
        
        return non_overlapping
    
    def get_entity_info(self, doc: Doc) -> List[Dict[str, Any]]:
        """
        Get detailed information about extracted entities.
        
        Args:
            doc: Processed spaCy Doc
            
        Returns:
            List of entity information dictionaries
        """
        entities_info = []
        
        for ent in doc.ents:
            entity_info = {
                'text': ent.text,
                'label': ent.label_,
                'start': ent.start_char,
                'end': ent.end_char,
                'description': self.entity_types.get(ent.label_, 'Unknown entity type')
            }
            entities_info.append(entity_info)
        
        return entities_info


def create_ner_component(nlp, name="scheduler_ner", model_path=None):
    """
    Factory function to create NER component.
    
    Args:
        nlp: spaCy language model
        name: Component name
        model_path: Path to trained NER model
        
    Returns:
        SchedulerNERComponent instance
    """
    return SchedulerNERComponent(nlp, name, model_path)


# Register the component factory
@spacy.component("scheduler_ner")
def make_scheduler_ner(nlp, name, model_path=None):
    return SchedulerNERComponent(nlp, name, model_path)


if __name__ == "__main__":
    # Example usage
    nlp = spacy.load("en_core_web_sm")
    ner_component = SchedulerNERComponent(nlp)
    
    # Add component to pipeline
    nlp.add_pipe(ner_component, name="scheduler_ner", last=True)
    
    # Test with sample text
    text = "Schedule a meeting with John Smith tomorrow at 2 PM in the conference room"
    doc = nlp(text)
    
    print("Extracted entities:")
    for ent in doc.ents:
        print(f"  {ent.text} ({ent.label_}): {ent.start_char}-{ent.end_char}")
    
    # Get detailed entity information
    entity_info = ner_component.get_entity_info(doc)
    print("\nDetailed entity information:")
    for info in entity_info:
        print(f"  {info}")
