"""
SAMPLE CODE - Text Classification pipeline component
"""

import spacy
from spacy.tokens import <PERSON>
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)


class SchedulerTextCatComponent:
    """Custom text classification component for scheduler requests."""
    
    def __init__(self, nlp, name="scheduler_textcat", model_path: Optional[str] = None):
        """
        Initialize the text classification component.
        
        Args:
            nlp: spaCy language model
            name: Component name
            model_path: Path to trained text classification model
        """
        self.nlp = nlp
        self.name = name
        self.model_path = model_path
        
        # Load trained model if provided
        if model_path:
            self.textcat_model = spacy.load(model_path)
        else:
            self.textcat_model = None
        
        # Define category types and their characteristics
        self.categories = {
            'MEETING': {
                'description': 'Meeting or conference requests',
                'keywords': ['meeting', 'conference', 'call', 'discussion', 'session'],
                'priority': 'high'
            },
            'REMINDER': {
                'description': 'Reminder and notification requests',
                'keywords': ['remind', 'notification', 'alert', 'notify'],
                'priority': 'medium'
            },
            'DEADLINE': {
                'description': 'Deadline and due date requests',
                'keywords': ['deadline', 'due', 'submit', 'complete', 'finish'],
                'priority': 'high'
            },
            'RECURRING': {
                'description': 'Recurring event requests',
                'keywords': ['recurring', 'weekly', 'daily', 'monthly', 'every', 'regular'],
                'priority': 'medium'
            },
            'CANCELLATION': {
                'description': 'Cancellation requests',
                'keywords': ['cancel', 'remove', 'delete', 'postpone', 'reschedule'],
                'priority': 'high'
            },
            'APPOINTMENT': {
                'description': 'Appointment scheduling requests',
                'keywords': ['appointment', 'visit', 'consultation', 'booking'],
                'priority': 'high'
            },
            'TASK': {
                'description': 'Task and to-do requests',
                'keywords': ['task', 'todo', 'work', 'assignment', 'project'],
                'priority': 'medium'
            },
            'OTHER': {
                'description': 'Other scheduling requests',
                'keywords': [],
                'priority': 'low'
            }
        }
        
        # Add extension to Doc for storing classification results
        if not Doc.has_extension("categories"):
            Doc.set_extension("categories", default={})
        if not Doc.has_extension("primary_category"):
            Doc.set_extension("primary_category", default="OTHER")
        if not Doc.has_extension("category_confidence"):
            Doc.set_extension("category_confidence", default=0.0)
    
    def __call__(self, doc: Doc) -> Doc:
        """
        Process a document and classify it into categories.
        
        Args:
            doc: spaCy Doc object
            
        Returns:
            Doc with classification results
        """
        if self.textcat_model:
            # Use trained model
            textcat_doc = self.textcat_model(doc.text)
            categories = textcat_doc.cats
        else:
            # Use rule-based classification
            categories = self._rule_based_classification(doc)
        
        # Store results in doc extensions
        doc._.categories = categories
        
        # Find primary category (highest score)
        primary_category = max(categories.items(), key=lambda x: x[1])
        doc._.primary_category = primary_category[0]
        doc._.category_confidence = primary_category[1]
        
        return doc
    
    def _rule_based_classification(self, doc: Doc) -> Dict[str, float]:
        """
        Rule-based text classification.
        
        Args:
            doc: spaCy Doc object
            
        Returns:
            Dictionary with category scores
        """
        text = doc.text.lower()
        scores = {category: 0.0 for category in self.categories.keys()}
        
        # Keyword-based scoring
        for category, info in self.categories.items():
            keyword_score = 0.0
            
            for keyword in info['keywords']:
                if keyword in text:
                    keyword_score += 1.0
            
            # Normalize by number of keywords
            if info['keywords']:
                keyword_score = keyword_score / len(info['keywords'])
            
            scores[category] = keyword_score
        
        # Entity-based scoring adjustments
        entities = [ent.label_ for ent in doc.ents]
        
        # Boost MEETING if PERSON entities present
        if 'PERSON' in entities:
            scores['MEETING'] += 0.3
            scores['APPOINTMENT'] += 0.2
        
        # Boost DEADLINE if DATE entities present
        if 'DATE' in entities:
            scores['DEADLINE'] += 0.2
            scores['MEETING'] += 0.1
        
        # Boost RECURRING if frequency words present
        frequency_words = ['every', 'weekly', 'daily', 'monthly', 'recurring']
        if any(word in text for word in frequency_words):
            scores['RECURRING'] += 0.4
        
        # Boost CANCELLATION if cancellation words present
        cancel_words = ['cancel', 'remove', 'delete', 'postpone']
        if any(word in text for word in cancel_words):
            scores['CANCELLATION'] += 0.5
        
        # Boost REMINDER if reminder words present
        reminder_words = ['remind', 'alert', 'notify', 'notification']
        if any(word in text for word in reminder_words):
            scores['REMINDER'] += 0.4
        
        # Normalize scores to sum to 1.0
        total_score = sum(scores.values())
        if total_score > 0:
            scores = {k: v / total_score for k, v in scores.items()}
        else:
            # Default to OTHER if no matches
            scores['OTHER'] = 1.0
        
        return scores
    
    def get_classification_details(self, doc: Doc) -> Dict[str, Any]:
        """
        Get detailed classification information.
        
        Args:
            doc: Processed spaCy Doc
            
        Returns:
            Dictionary with classification details
        """
        categories = doc._.categories
        primary_category = doc._.primary_category
        confidence = doc._.category_confidence
        
        # Sort categories by score
        sorted_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)
        
        details = {
            'primary_category': primary_category,
            'confidence': confidence,
            'all_categories': categories,
            'sorted_categories': sorted_categories,
            'category_info': self.categories.get(primary_category, {}),
            'priority': self.categories.get(primary_category, {}).get('priority', 'low'),
            'top_3_categories': sorted_categories[:3]
        }
        
        return details
    
    def classify_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """
        Classify a batch of texts.
        
        Args:
            texts: List of text strings
            
        Returns:
            List of classification results
        """
        results = []
        
        for text in texts:
            doc = self.nlp(text)
            doc = self(doc)  # Apply classification
            details = self.get_classification_details(doc)
            
            results.append({
                'text': text,
                'classification': details
            })
        
        return results
    
    def get_category_statistics(self, docs: List[Doc]) -> Dict[str, Any]:
        """
        Get statistics about classifications across multiple documents.
        
        Args:
            docs: List of processed spaCy Docs
            
        Returns:
            Dictionary with statistics
        """
        category_counts = {category: 0 for category in self.categories.keys()}
        confidence_scores = []
        
        for doc in docs:
            primary_category = doc._.primary_category
            confidence = doc._.category_confidence
            
            category_counts[primary_category] += 1
            confidence_scores.append(confidence)
        
        total_docs = len(docs)
        avg_confidence = sum(confidence_scores) / max(total_docs, 1)
        
        # Calculate percentages
        category_percentages = {
            category: (count / total_docs) * 100 
            for category, count in category_counts.items()
        }
        
        statistics = {
            'total_documents': total_docs,
            'category_counts': category_counts,
            'category_percentages': category_percentages,
            'average_confidence': avg_confidence,
            'confidence_distribution': {
                'min': min(confidence_scores) if confidence_scores else 0,
                'max': max(confidence_scores) if confidence_scores else 0,
                'avg': avg_confidence
            }
        }
        
        return statistics


def create_textcat_component(nlp, name="scheduler_textcat", model_path=None):
    """
    Factory function to create text classification component.
    
    Args:
        nlp: spaCy language model
        name: Component name
        model_path: Path to trained text classification model
        
    Returns:
        SchedulerTextCatComponent instance
    """
    return SchedulerTextCatComponent(nlp, name, model_path)


# Register the component factory
@spacy.component("scheduler_textcat")
def make_scheduler_textcat(nlp, name, model_path=None):
    return SchedulerTextCatComponent(nlp, name, model_path)


if __name__ == "__main__":
    # Example usage
    nlp = spacy.load("en_core_web_sm")
    textcat_component = SchedulerTextCatComponent(nlp)
    
    # Add component to pipeline
    nlp.add_pipe(textcat_component, name="scheduler_textcat", last=True)
    
    # Test with sample texts
    test_texts = [
        "Schedule a meeting with John Smith tomorrow at 2 PM",
        "Remind me to call the client next Friday",
        "Cancel my appointment with Dr. Johnson",
        "Set up a weekly team standup every Monday",
        "The project deadline is December 15th"
    ]
    
    print("Text Classification Results:")
    print("=" * 50)
    
    for text in test_texts:
        doc = nlp(text)
        details = textcat_component.get_classification_details(doc)
        
        print(f"\nText: {text}")
        print(f"Category: {details['primary_category']} (confidence: {details['confidence']:.2f})")
        print(f"Priority: {details['priority']}")
        print(f"Top 3 categories: {details['top_3_categories'][:3]}")
    
    # Batch classification
    print("\n" + "=" * 50)
    print("Batch Classification:")
    batch_results = textcat_component.classify_batch(test_texts)
    
    for result in batch_results:
        classification = result['classification']
        print(f"{result['text'][:50]}... -> {classification['primary_category']}")
