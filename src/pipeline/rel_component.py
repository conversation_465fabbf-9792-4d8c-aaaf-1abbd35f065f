"""
SAMPLE CODE - Relation Extraction pipeline component
"""

import spacy
from spacy.tokens import Doc, Span
from typing import List, Dict, Any, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class SchedulerRelationComponent:
    """Custom relation extraction component for scheduler entities."""
    
    def __init__(self, nlp, name="scheduler_relations", model_path: Optional[str] = None):
        """
        Initialize the relation extraction component.
        
        Args:
            nlp: spaCy language model
            name: Component name
            model_path: Path to trained relation model
        """
        self.nlp = nlp
        self.name = name
        self.model_path = model_path
        
        # Define relation types
        self.relation_types = {
            'SCHEDULED_FOR': 'Entity is scheduled for a specific time/date',
            'AT_TIME': 'Event occurs at specific time',
            'AT_LOCATION': 'Event occurs at specific location',
            'WITH_PERSON': 'Event involves specific person',
            'BEFORE_TIME': 'Event must occur before specific time',
            'AFTER_TIME': 'Event must occur after specific time',
            'RECURRING_ON': 'Event recurs on specific pattern',
            'DURATION_OF': 'Event has specific duration',
            'ASSIGNED_TO': 'Task is assigned to person'
        }
        
        # Add extension to Doc for storing relations
        if not Doc.has_extension("relations"):
            Doc.set_extension("relations", default=[])
    
    def __call__(self, doc: Doc) -> Doc:
        """
        Process a document and extract relations between entities.
        
        Args:
            doc: spaCy Doc object with entities
            
        Returns:
            Doc with extracted relations
        """
        relations = self._extract_relations(doc)
        doc._.relations = relations
        return doc
    
    def _extract_relations(self, doc: Doc) -> List[Dict[str, Any]]:
        """
        Extract relations between entities in the document.
        
        Args:
            doc: spaCy Doc object
            
        Returns:
            List of relation dictionaries
        """
        relations = []
        entities = list(doc.ents)
        
        # Extract pairwise relations
        for i, ent1 in enumerate(entities):
            for j, ent2 in enumerate(entities):
                if i != j:
                    relation = self._classify_relation(doc, ent1, ent2)
                    if relation:
                        relations.append({
                            'head': i,
                            'tail': j,
                            'head_entity': {
                                'text': ent1.text,
                                'label': ent1.label_,
                                'start': ent1.start_char,
                                'end': ent1.end_char
                            },
                            'tail_entity': {
                                'text': ent2.text,
                                'label': ent2.label_,
                                'start': ent2.start_char,
                                'end': ent2.end_char
                            },
                            'relation': relation,
                            'confidence': self._calculate_confidence(doc, ent1, ent2, relation)
                        })
        
        return relations
    
    def _classify_relation(self, doc: Doc, ent1: Span, ent2: Span) -> Optional[str]:
        """
        Classify the relation between two entities.
        
        Args:
            doc: spaCy Doc object
            ent1: First entity
            ent2: Second entity
            
        Returns:
            Relation type or None if no relation
        """
        # Get the text between entities
        start_pos = min(ent1.end, ent2.end)
        end_pos = max(ent1.start, ent2.start)
        
        if start_pos < end_pos:
            between_text = doc[start_pos:end_pos].text.lower()
        else:
            between_text = ""
        
        # Rule-based relation classification
        relation = None
        
        # SCHEDULED_FOR relations
        if ent1.label_ in ['TASK', 'PERSON'] and ent2.label_ in ['DATE', 'TIME']:
            if any(word in between_text for word in ['on', 'at', 'for', 'during']):
                relation = 'SCHEDULED_FOR'
            elif abs(ent1.start - ent2.start) < 5:  # Close proximity
                relation = 'SCHEDULED_FOR'
        
        # AT_TIME relations
        elif ent1.label_ == 'TASK' and ent2.label_ == 'TIME':
            if 'at' in between_text or abs(ent1.start - ent2.start) < 3:
                relation = 'AT_TIME'
        
        # AT_LOCATION relations
        elif ent1.label_ == 'TASK' and ent2.label_ == 'LOCATION':
            if any(word in between_text for word in ['in', 'at', 'from']):
                relation = 'AT_LOCATION'
        
        # WITH_PERSON relations
        elif ent1.label_ == 'TASK' and ent2.label_ == 'PERSON':
            if 'with' in between_text:
                relation = 'WITH_PERSON'
        
        # BEFORE_TIME relations
        elif ent1.label_ == 'TASK' and ent2.label_ in ['DATE', 'TIME']:
            if 'before' in between_text:
                relation = 'BEFORE_TIME'
        
        # AFTER_TIME relations
        elif ent1.label_ == 'TASK' and ent2.label_ in ['DATE', 'TIME']:
            if 'after' in between_text:
                relation = 'AFTER_TIME'
        
        # RECURRING_ON relations
        elif ent1.label_ == 'TASK' and ent2.label_ == 'DATE':
            if any(word in between_text for word in ['every', 'each', 'weekly', 'daily', 'monthly']):
                relation = 'RECURRING_ON'
        
        # ASSIGNED_TO relations
        elif ent1.label_ == 'TASK' and ent2.label_ == 'PERSON':
            if any(word in between_text for word in ['assigned to', 'for', 'by']):
                relation = 'ASSIGNED_TO'
        
        return relation
    
    def _calculate_confidence(self, doc: Doc, ent1: Span, ent2: Span, relation: str) -> float:
        """
        Calculate confidence score for a relation.
        
        Args:
            doc: spaCy Doc object
            ent1: First entity
            ent2: Second entity
            relation: Relation type
            
        Returns:
            Confidence score between 0 and 1
        """
        # Simple confidence calculation based on distance and keywords
        distance = abs(ent1.start - ent2.start)
        
        # Base confidence decreases with distance
        base_confidence = max(0.1, 1.0 - (distance / 20.0))
        
        # Boost confidence for specific keyword patterns
        start_pos = min(ent1.end, ent2.end)
        end_pos = max(ent1.start, ent2.start)
        
        if start_pos < end_pos:
            between_text = doc[start_pos:end_pos].text.lower()
            
            # Keyword-based confidence boosts
            keyword_boosts = {
                'SCHEDULED_FOR': ['on', 'at', 'for', 'during'],
                'AT_TIME': ['at'],
                'AT_LOCATION': ['in', 'at', 'from'],
                'WITH_PERSON': ['with'],
                'BEFORE_TIME': ['before'],
                'AFTER_TIME': ['after'],
                'RECURRING_ON': ['every', 'each', 'weekly', 'daily'],
                'ASSIGNED_TO': ['assigned to', 'for', 'by']
            }
            
            if relation in keyword_boosts:
                for keyword in keyword_boosts[relation]:
                    if keyword in between_text:
                        base_confidence = min(1.0, base_confidence + 0.3)
                        break
        
        return round(base_confidence, 2)
    
    def get_relations_summary(self, doc: Doc) -> Dict[str, Any]:
        """
        Get a summary of extracted relations.
        
        Args:
            doc: Processed spaCy Doc
            
        Returns:
            Dictionary with relation summary
        """
        relations = doc._.relations
        
        summary = {
            'total_relations': len(relations),
            'relation_types': {},
            'high_confidence_relations': [],
            'entities_involved': set()
        }
        
        for relation in relations:
            rel_type = relation['relation']
            
            # Count relation types
            if rel_type not in summary['relation_types']:
                summary['relation_types'][rel_type] = 0
            summary['relation_types'][rel_type] += 1
            
            # Track high confidence relations
            if relation['confidence'] > 0.7:
                summary['high_confidence_relations'].append(relation)
            
            # Track entities involved in relations
            summary['entities_involved'].add(relation['head_entity']['text'])
            summary['entities_involved'].add(relation['tail_entity']['text'])
        
        summary['entities_involved'] = list(summary['entities_involved'])
        
        return summary
    
    def visualize_relations(self, doc: Doc) -> str:
        """
        Create a text visualization of relations.
        
        Args:
            doc: Processed spaCy Doc
            
        Returns:
            String representation of relations
        """
        relations = doc._.relations
        
        if not relations:
            return "No relations found."
        
        viz = "Extracted Relations:\n"
        viz += "=" * 50 + "\n"
        
        for i, relation in enumerate(relations, 1):
            head = relation['head_entity']
            tail = relation['tail_entity']
            rel_type = relation['relation']
            confidence = relation['confidence']
            
            viz += f"{i}. {head['text']} ({head['label']}) "
            viz += f"--[{rel_type}]--> "
            viz += f"{tail['text']} ({tail['label']}) "
            viz += f"(confidence: {confidence})\n"
        
        return viz


def create_relation_component(nlp, name="scheduler_relations", model_path=None):
    """
    Factory function to create relation extraction component.
    
    Args:
        nlp: spaCy language model
        name: Component name
        model_path: Path to trained relation model
        
    Returns:
        SchedulerRelationComponent instance
    """
    return SchedulerRelationComponent(nlp, name, model_path)


# Register the component factory
@spacy.component("scheduler_relations")
def make_scheduler_relations(nlp, name, model_path=None):
    return SchedulerRelationComponent(nlp, name, model_path)


if __name__ == "__main__":
    # Example usage
    nlp = spacy.load("en_core_web_sm")
    
    # Add NER component first (assuming it exists)
    from .ner_component import SchedulerNERComponent
    ner_component = SchedulerNERComponent(nlp)
    nlp.add_pipe(ner_component, name="scheduler_ner", last=True)
    
    # Add relation component
    rel_component = SchedulerRelationComponent(nlp)
    nlp.add_pipe(rel_component, name="scheduler_relations", last=True)
    
    # Test with sample text
    text = "Schedule a meeting with John Smith tomorrow at 2 PM in the conference room"
    doc = nlp(text)
    
    print("Extracted entities:")
    for ent in doc.ents:
        print(f"  {ent.text} ({ent.label_})")
    
    print("\nExtracted relations:")
    print(rel_component.visualize_relations(doc))
    
    print("\nRelation summary:")
    summary = rel_component.get_relations_summary(doc)
    for key, value in summary.items():
        print(f"  {key}: {value}")
