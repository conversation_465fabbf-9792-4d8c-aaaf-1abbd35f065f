import spacy

# Load small English pipeline (includes tokenizer, POS, NER, etc.)
nlp = spacy.load("en_core_web_sm")

# Example text
text = "Finish math assignment by 5pm in the library."

# Process text
doc = nlp(text)

# Print tokens
print("Tokens:")
for token in doc:
    print(token.text)

# Optional: show token attributes
print("\nTokens with details:")
for token in doc:
    print(f"Text: {token.text}, Lemma: {token.lemma_}, POS: {token.pos_}, Dep: {token.dep_}")
