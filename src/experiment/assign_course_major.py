import re
import pandas as pd
import numpy as np
import pandas as pd 
import os 

# read full path to csv file
full_path = os.path.join(os.path.dirname(__file__), 'data.csv')
full_path_two = os.path.join(os.path.dirname(__file__), 'students.csv')
pd.set_option("display.max_rows", None)      # show all rows
pd.set_option("display.max_columns", None)   # show all columns
pd.set_option("display.width", None)         # don't wrap columns
pd.set_option("display.max_colwidth", None)  # show full cell content

df = pd.read_csv(full_path)

df_students = pd.read_csv(full_path_two)

df = df[["CourseID", "CourseName"]]

df_students = df_students[["Major"]].drop_duplicates()

# --- example: your input DataFrames (skip if you already have them) ---
# df = pd.DataFrame([...])  # must contain CourseID, CourseName
# df_students = pd.DataFrame([...])  # must contain Major

# --- keyword rules for each major (tweak keywords if you want different mapping) ---
major_keywords = {
    "Computer Science": [
        "computer", "computer science", "programming", "software", "software engineering",
        "data structures", "database", "databases", "algorithms", "operating system", "operating systems",
        "networking", "network", "cybersecurity", "artificial intelligence", "ai", "machine learning",
        "software engineering", "software"
    ],
    "Literature": ["literature", "poetry", "novel", "short story", "literary"],
    "Mathematics": ["math", "mathematics", "calculus", "algebra", "geometry", "analysis"],
    "Philosophy": ["philosophy", "ethics", "logic"],
    "Statistics": ["statistics", "statistical"],
    "Engineering": ["engineering", "engineering design"],
    "Physics": ["physics", "thermodynamics", "quantum"],
    "Economics": ["economics", "economy"],
    "History": ["history", "histor"],
    "Biology": ["biology", "bio"],
    # Add more majors / tweak keywords as needed
}

# If df_students contains a different subset of majors, restrict mapping to those majors:
if 'df_students' in globals():
    majors_present = set(df_students["Major"].dropna().unique().astype(str))
    # Filter major_keywords to only keys present in df_students (keeps order)
    major_keywords = {m: kws for m, kws in major_keywords.items() if m in majors_present}

# --- matching function ---
def find_majors_for_course(course_name):
    name = str(course_name)
    matches = []
    for major, keywords in major_keywords.items():
        # build regex for keywords with word boundaries (escape special chars)
        patt = r"\b(?:" + "|".join(re.escape(k.lower()) for k in keywords) + r")\b"
        if re.search(patt, name.lower()):
            matches.append(major)
    return matches

# --- produce associations CourseID -> Major (one row per association) ---
associations = []
for _, row in df.iterrows():
    cid = row["CourseID"]
    cname = row["CourseName"]
    matched = find_majors_for_course(cname)

    if not matched:
        # fallback: unassigned so you can inspect later
        associations.append({"CourseID": cid, "CourseName": cname, "Major": "Unassigned"})
    else:
        for m in matched:
            associations.append({"CourseID": cid, "CourseName": cname, "Major": m})

result_df = pd.DataFrame(associations)

# Optional: remove duplicates if the same (CourseID, Major) was added twice
result_df = result_df.drop_duplicates(subset=["CourseID", "Major"]).reset_index(drop=True)

# Save to CSV
result_df.to_csv("courseid_major_mapping.csv", index=False)

# Show summary and any unassigned courses for tuning
print("Mappings created:", len(result_df))
print(result_df.head(20))

unassigned = result_df[result_df["Major"] == "Unassigned"]
if not unassigned.empty:
    print("\nUnassigned courses (please review keywords):")
    print(unassigned[["CourseID", "CourseName"]].drop_duplicates().to_string(index=False))
else:
    print("\nAll courses were assigned to at least one major.")

# If you want a one-to-many pivot (majors as list per CourseID):
pivot = result_df.groupby(["CourseID", "CourseName"])["Major"].apply(list).reset_index()
pivot.to_csv("courseid_majors_list.csv", index=False)
