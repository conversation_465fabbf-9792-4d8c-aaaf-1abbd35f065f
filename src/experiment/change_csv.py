import numpy as np
import pandas as pd 
import os 

# read full path to csv file
full_path = os.path.join(os.path.dirname(__file__), 'data.csv')
full_path_two = os.path.join(os.path.dirname(__file__), 'students.csv')
pd.set_option("display.max_rows", None)      # show all rows
pd.set_option("display.max_columns", None)   # show all columns
pd.set_option("display.width", None)         # don't wrap columns
pd.set_option("display.max_colwidth", None)  # show full cell content

df = pd.read_csv(full_path)
df_students= pd.read_csv(full_path_two)
# # first column is year semester and it is sorted
# col = "Year"  # the column you want to move first
# df_courses = df_courses[[col] + [c for c in df_courses.columns if c != col]]
# sort by CourseID

# df_courses = df_courses.sort_values(by='CourseID')
# df_courses.to_csv("semester_course.csv", index=False)



# df_students = pd.read_csv(full_path_two)

df_students = df_students[["CourseID", "CourseName"]]
print(df_students)
# columns = list(df.columns())
# columns = columns
# import pandas as pd

# rename column Instructor to InstructorName
# df = df.rename(columns={"Instructor": "InstructorName"})


# read instructors file
# df_students = pd.read_csv(full_path_two)  
# e.g. InstructorID,InstructorName

# Add columnts status and Semester
# count = 0
# for index, row in df_students.iterrows():
#     df_students.at[index, "Semester"] = 1 if df_students.at[index, "Semester"] == "Spring" else 2 if df_students.at[index, "Semester"] == "Summer" else 3
#     count += 1

# df = df[["CourseID", "CourseName", "Semester"]]
# df =df.groupby("Semester").apply(lambda x: x.values.tolist()).reset_index()
# print(df)

# merge: match InstructorName in both files
# df = df.merge(df_instructors, on="InstructorName", how="left")
# remove column InstructorName
# df = df.drop(columns=["InstructorName"])
# df.to_csv("semester_course.csv", index=False)
# now df has CourseID, CourseName, InstructorName, InstructorID
# print(df)
# df_students = df_students[["StudentID"]]
df_students.to_csv("students.csv", index=False)

# df_students = df_students[["Year"]].drop_duplicates()
# print(df_students)



# df_merge = pd.merge(df, df_students, on="Major", how="left")


# print(set(df["Instructor"]))
# change instructor name 

# df["Instructor"] = df["Instructor"].replace("Prof. Chen", "Dr. Miller")
# df["Instructor"] = df["Instructor"].replace("Prof. Davis", "Dr. Johnson")
# df["Instructor"] = df["Instructor"].replace("Prof. Smith", "Dr. Kumar")

# df = df[["CourseID", "Semester", "Instructor"]]
# df =df.groupby(["Semester", "Instructor"])[["CourseID"]].apply(lambda x: x.values.tolist()).reset_index()

# df = df[["StudentID", "Major", ]]
# df =df.groupby(["Major"])[["StudentID"]].apply(lambda x: x.values.tolist()).reset_index()
# print(df)
# df = df.groupby(["Semester", "Instructor"])[["CourseName"]].apply(lambda x: x.values.tolist()).reset_index()


# df.to_json("students.json", orient="records")

# print(groups.to_dict())
# df.to_csv(full_path, index=False)