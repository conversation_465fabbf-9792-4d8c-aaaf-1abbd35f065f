# import json

# # Load JSON
# with open("students.json") as f:
#     data = json.load(f)

# # Flatten the "0" lists
# for entry in data:
#     if "0" in entry:
#         # each element is ["Course"], so take the first element from each
#         entry["0"] = [item[0] for item in entry["0"]]

# # Save back nicely formatted
# with open("formatted.json", "w") as f:
#     json.dump(data, f, indent=4)

# import json

# # Load your JSON (from file or string)
# with open("students.json") as f:
#     data = json.load(f)

# # Write back formatted JSON
# with open("formatted.json", "w") as f:
#     json.dump(data, f, indent=4)   # indent=4 makes it pretty

# import json

# # Load JSON
# with open("students.json") as f:
#     data = json.load(f)

# # Transform into new structure
# formatted = {entry["Major"]: entry["0"] for entry in data}

# # Save nicely formatted
# with open("formatted.json", "w") as f:
#     json.dump(formatted, f, indent=4)


# import json
# import csv

# # Load your JSON
# with open("formatted.json") as f:
#     data = json.load(f)

# # Open CSV for writing
# with open("majors.csv", "w", newline="") as f:
#     writer = csv.writer(f)
#     writer.writerow(["Major", "Code"])  # header

#     # Flatten JSON
#     for entry in data:
#         major = entry["Major"]
#         for code in entry["0"]:
#             writer.writerow([major, code])
 
 
import json
import csv
from collections import OrderedDict

# --- load JSON from file ---
with open("formatted.json", "r", encoding="utf-8") as f:
    data = json.load(f)   # expected: list of objects

# Desired semester ordering
semester_order = ["Spring", "Summer", "Fall"]
semester_set = set(semester_order)

# Build an ordered list of entries per semester (preserve JSON order)
sem_map = OrderedDict((s, []) for s in semester_order)
skipped = []
for entry in data:
    sem = entry.get("Semester")
    instr = entry.get("Instructor")
    if sem in semester_set and instr:
        sem_map[sem].append(entry)
    else:
        # collect entries that are malformed or have unexpected/missing semester
        skipped.append(entry)

# Determine the instructor ordering:
# 1) Use instructors order from the first semester (in semester_order) that has entries
# 2) Then append any other instructors seen elsewhere in their first-appearance order
seen = []
for s in semester_order:
    if sem_map[s]:
        for e in sem_map[s]:
            instr = e.get("Instructor")
            if instr and instr not in seen:
                seen.append(instr)
        break

# append any other instructors not already in seen (keep original data order)
for e in data:
    instr = e.get("Instructor")
    if instr and instr not in seen:
        seen.append(instr)

instructors_order = seen

# Build quick lookup: semester -> instructor -> list_of_courseIDs
sem_instr_courses = {s: {} for s in semester_order}
for entry in data:
    sem = entry.get("Semester")
    instr = entry.get("Instructor")
    if sem in semester_set and instr:
        # courses may be in key "0" (as in your example)
        courses = entry.get("0") or entry.get("courses") or []
        # ensure list
        if not isinstance(courses, list):
            courses = [courses]
        sem_instr_courses[sem].setdefault(instr, []).extend(courses)

# Flatten into rows in the exact order: semesters (spring,summer,fall) -> instructors_order
rows = []
for sem in semester_order:
    for instr in instructors_order:
        course_list = sem_instr_courses[sem].get(instr, [])
        for cid in course_list:
            rows.append({"Semester": sem, "Instructor": instr, "CourseID": cid})

# Write CSV
out_csv = "semester_instructor_courseid.csv"
with open(out_csv, "w", newline="", encoding="utf-8") as f:
    writer = csv.DictWriter(f, fieldnames=["Semester", "Instructor", "CourseID"])
    writer.writeheader()
    writer.writerows(rows)

# Summary / preview
print(f"Wrote {len(rows)} rows to {out_csv}")
print("Semesters order:", semester_order)
print("Instructor order used:", instructors_order)
if skipped:
    print(f"Skipped {len(skipped)} malformed/unknown-semester entries (inspect 'skipped' variable).")

# small preview
for r in rows[:50]:
    print(r)
