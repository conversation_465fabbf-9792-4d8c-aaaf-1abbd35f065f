import re
import spacy
from typing import List, Dict, Any

class TaskEntityExtractor:
    def __init__(self):
        # Load spacy model (you may need to install: python -m spacy download en_core_web_sm)
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            print("SpaCy model not found. Install with: python -m spacy download en_core_web_sm")
            self.nlp = None
        
        # Define pattern mappings
        self.priority_patterns = {
            'HIGH': ['important', 'urgent', 'critical', 'priority', 'crucial'],
            'MEDIUM': ['need to', 'should', 'must', 'have to', 'required'],
            'LOW': ['plan to', 'want to', 'would like', 'entertainment', 'fun', 'relaxation']
        }
        
        self.commitment_patterns = {
            'MANDATORY': ['must', 'have to', 'need to', 'required'],
            'COMMITTED': ['will', 'going to', 'plan to'],
            'PREFERRED': ['want to', 'would like', 'hoping to'],
            'EXPERIMENTAL': ['might', 'try', 'experiment', 'maybe']
        }
        
        self.classic_patterns = {
            'EDUCATIONAL': ['course', 'study', 'learn', 'chapter', 'module', 'lecture', 'class'],
            'ORGANIZATIONAL': ['meeting', 'talk to', 'pay', 'write report', 'email', 'call'],
            'HEALTH_FITNESS': ['gym', 'exercise', 'workout', 'yoga', 'run', 'fitness'],
            'SELF_DEVELOPMENT': ['practice', 'skill', 'hobby', 'cook', 'create', 'build'],
            'CONSISTENCY': ['daily', 'routine', 'habit', 'regularly'],
            'NEW_EXPERIENCE': ['try new', 'visit', 'explore', 'first time']
        }
        
        
   
        self.personal_category_patterns = {
            'EDUCATIONAL': [
                'study', 'research', 'code', 'implement', 'analyze',
                'read', 'revise', 'attend', 'practice', 'debug',
                'assignment', 'project', 'tutorial', 'exam', 'certification',
                'lecture_notes', 'textbook', 'presentation', 'portfolio', 'mentor', 
            ],
            'ORGANIZATIONAL': [
                'schedule', 'plan', 'organize', 'coordinate', 'invoice',
                'pitch', 'network', 'prioritize', 'delegate', 'manage',
                'contract', 'deadline', 'proposal', 'brief', 'workflow',
                'client', 'team', 'platform', 'portfolio', 'feedback'
            ],
            'HEALTH_FITNESS': [
                'stretch', 'exercise', 'run', 'practice_yoga', 'walk',
                'meditate', 'hydrate', 'sleep', 'train', 'cycle',
                'balance', 'focus', 'energy', 'mindset', 'discipline',
                'routine', 'breathing', 'strength', 'posture', 'wellness'
            ],
            'SELF_DEVELOPMENT': [
                'design', 'prototype', 'sketch', 'iterate', 'practice',
                'improve', 'build', 'test', 'critique', 'showcase', 'start making',
                'creativity', 'innovation', 'portfolio', 'skillset', 'growth',
                'discipline', 'branding', 'independence', 'confidence', 'vision', "CV", "resume", "interview"
            ],
            'CONSISTENCY': [
                'practice', 'repeat', 'track', 'log', 'review',
                'maintain', 'commit', 'habitualize', 'persist', 'schedule',
                'progress', 'routine', 'milestone', 'planner', 'timeline',
                'stability', 'habit', 'journal', 'daily_goal', 'checklist'
            ],
            'NEW_EXPERIENCE': [
                'travel', 'explore', 'visit', 'discover', 'try',
                'experience', 'taste', 'navigate', 'photograph', 'meet',
                'journey', 'adventure', 'destination', 'culture', 'festival',
                'cuisine', 'landmark', 'community', 'trip', 'memory', "cooking"
            ]
        }

        
        self.category_patterns = {
            category: list(set(self.classic_patterns.get(category, [])) | set(self.personal_category_patterns.get(category, [])))
            for category in set(self.classic_patterns) | set(self.personal_category_patterns)
        }
        
        self.action_verbs = [
            'talk', 'start', 'complete', 'finish', 'pay', 'write', 'go', 'cook', 'order',
            'study', 'read', 'practice', 'visit', 'try', 'make', 'create', 'build'
        ]
        
        self.temporal_modifiers = [
            'today', 'tomorrow', 'yesterday', 'this week', 'next week', 'this evening',
            'morning', 'afternoon', 'tonight'
        ]
        
        self.intent_patterns = {
            'CREATE_NEW': ['start', 'begin', 'create', 'make', 'build'],
            'CONTINUE_EXISTING': ['continue', 'keep', 'maintain', 'finish', 'complete'],
            'ONE_TIME_INSTANCE': ['today', 'tomorrow', 'once'],
            'TRY_NEW': ['try', 'experiment', 'test'],
            'REPLACE_EXISTING': ['instead of', 'replace', 'swap', 'change from']
        }
        
        
        
         # TIME_CONSTRAINT patterns
        self.fixed_time_patterns = [
            r'\b\d{1,2}:\d{2}\b',  # 9:30, 14:45
            r'\b\d{1,2}:\d{2}-\d{1,2}:\d{2}\b',  # 10:15-12:45
            r'\bat \d{1,2} (AM|PM|am|pm)\b',  # at 9 AM
            r'\bat \d{1,2}:\d{2} (AM|PM|am|pm)\b',  # at 9:30 AM
            r'\bfrom \d{1,2}:\d{2} to \d{1,2}:\d{2}\b'  # from 9:00 to 11:00
        ]
        
        self.duration_patterns = [
            r'\b\d+\s+(minutes?|mins?|hours?|hrs?)\b',  # 30 minutes, 2 hours
            r'\bfor \d+\s+(minutes?|mins?|hours?|hrs?)\b',  # for 30 minutes
            r'\b(half an hour|one hour|two hours)\b'  # written numbers
        ]
        
        self.deadline_patterns = [
            r'\bby (tomorrow|today|tonight|morning|evening)\b',
            r'\bbefore \d{1,2} (AM|PM|am|pm)\b',  # before 5 PM
            r'\bby \d{1,2}:\d{2}\b',  # by 14:30
            r'\bdue (tomorrow|today|tonight)\b',
            r'\bdeadline (tomorrow|today|tonight)\b'
        ]
        
        # RECURRENCE_PATTERN patterns
        self.recurrence_patterns = {
            'DAILY': ['every day', 'daily', 'each day', 'per day'],
            'WEEKLY': ['weekly', 'once a week', 'every week', 'per week'],
            'SPECIFIC_DAYS': ['monday and friday', 'weekends', 'weekdays', 'tuesday and thursday', 'mon wed fri'],
            'FREQUENCY_COUNT': [
                r'\b\d+ times? (per|a) (day|week|month)\b',  # 3 times per week
                r'\btwice (daily|weekly|monthly)\b',  # twice daily
                r'\bonce (daily|weekly|monthly)\b'  # once daily
            ],
            'UNTIL_GOAL': ['until completed', 'until finished', 'until I reach', 'until level', 'until done']
        }
        
        # REPLACEMENT_INDICATOR patterns
        self.replacement_indicator_patterns = [
            'instead of', 'replace', 'swap with', 'change from', 'substitute',
            'rather than', 'in place of', 'switch to', 'drop and do'
        ]
        
        # Unit patterns (for measurable targets)
        self.unit_patterns = [
            r'\b(one|two|three|four|five|six|seven|eight|nine|ten|\d+)\s+(chapter|chapters)\b',
            r'\b(one|two|three|four|five|six|seven|eight|nine|ten|\d+)\s+(video|videos)\b',
            r'\b(one|two|three|four|five|six|seven|eight|nine|ten|\d+)\s+(book|books)\b',
            r'\b(one|two|three|four|five|six|seven|eight|nine|ten|\d+)\s+(page|pages)\b',
            r'\b(one|two|three|four|five|six|seven|eight|nine|ten|\d+)\s+(exercise|exercises)\b',
            r'\b(one|two|three|four|five|six|seven|eight|nine|ten|\d+)\s+(module|modules)\b',
            r'\b(one|two|three|four|five|six|seven|eight|nine|ten|\d+)\s+(lesson|lessons)\b'
        ]
        
        # COMPLETION_CRITERIA patterns
        self.completion_criteria_patterns = [
            'until finished', 'until completed', 'until done',
            r'reach level \d+', r'until level \d+',
            'complete the course', 'finish the program',
            'get to 100%', 'achieve mastery',
            'pass the exam', 'get certified'
        ]
        
        # PROGRESS_INDICATOR patterns
        self.progress_indicator_patterns = [
            r'continue from (chapter|page|lesson|module) \d+',
            r'starting from (chapter|page|lesson|module) \d+',
            'maintain current streak', 'keep current pace',
            'build on yesterday\'s work', 'continue where I left off',
            r'resume from \w+', 'pick up where',
            'current progress', 'so far completed'
        ]
        
        # FLEXIBILITY_INDICATOR patterns
        self.flexibility_indicator_patterns = {
            'FIXED': ['must be at', 'exactly at', 'strict timing', 'cannot be moved', 'fixed time'],
            'FLEXIBLE': ['around', 'approximately', 'roughly', 'about', 'flexible timing'],
            'OPTIMIZABLE': ['whenever', 'any time', 'flexible', 'as needed', 'when convenient']
        }
        
        # EXTERNAL_CONSTRAINT patterns
        self.external_constraint_patterns = [
            'class', 'meeting', 'appointment', 'deadline', 'interview',
            'conference call', 'presentation', 'exam', 'workshop',
            'scheduled', 'booked', 'reserved', 'committed'
        ]
        
        # FREQUENCY_MODIFIER patterns
        self.frequency_modifier_patterns = [
            'more often', 'more frequently', 'increase frequency',
            'less frequently', 'less often', 'reduce frequency',
            'double the frequency', 'twice as often',
            'half as often', 'every other', 'skip every'
        ]
        
        # CONDITION_MODIFIER patterns
        self.condition_modifier_patterns = [
            'if weather permits', 'if available', 'if possible',
            'when at home', 'when at office', 'when free',
            'after work hours', 'before breakfast', 'during lunch',
            'only if', 'provided that', 'assuming',
            'weather permitting', 'if time allows'
        ]
        
        

    def extract_tasks_from_text(self, text: str) -> List[str]:
        """Split text into individual tasks"""
        # Split by common task separators
        tasks = []
        
        # First split by sentences/semicolons/commas followed by action verbs
        # Split text into chunks by punctuation or 'and'
        last_end = 0
        for match in re.finditer(r'[.;,]|\band\b', text):
            chunk = text[last_end:match.start()].strip()
            if len(chunk) >= 10:
                words = chunk.lower().split()
                if any(verb in words for verb in self.action_verbs):
                    tasks.append((chunk, last_end))
            last_end = match.end()
        
        # Handle the final chunk after the last match
        final_chunk = text[last_end:].strip()
        if len(final_chunk) >= 10:
            words = final_chunk.lower().split()
            if any(verb in words for verb in self.action_verbs):
                tasks.append((final_chunk, last_end))
                
        return tasks
    
    
    
    def extract_task_action(self, text: str) -> tuple:
        """Extract TASK_ACTION entity"""
        words = text.lower().split()
        
        for i, word in enumerate(words):
            if word in self.action_verbs:
                # Handle compound actions like "start making"
                if i + 1 < len(words) and words[i + 1] in ['making', 'doing', 'working']:
                    return words[i] + " " + words[i + 1], "TASK_ACTION", words[i] + " " + words[i + 1]
                return word, "TASK_ACTION", word
        
        # Fallback: find first verb
        if self.nlp:
            doc = self.nlp(text)
            for token in doc:
                if token.pos_ == 'VERB':
                    return token.text, "TASK_ACTION", token.text
        
        return None, None, None
    
    
    def extract_task_object(self, text: str, action_word: str) -> tuple:
        """Extract TASK_OBJECT entity"""
        if not action_word:
            return None, None, None
            
        # Find everything after the action verb
        text_lower = text.lower()
        action_pos = text_lower.find(action_word.lower())
        
        if action_pos != -1:
            object_part = text[action_pos + len(action_word):].strip()
            # Clean up common prefixes
            object_part = re.sub(r'^(to|the|a|an)\s+', '', object_part, flags=re.IGNORECASE)
            if object_part:
                return object_part, "TASK_OBJECT", object_part
        
        return None, None, None

    def extract_priority_level(self, text: str) -> tuple:
        """Extract PRIORITY_LEVEL entity"""
        text_lower = text.lower()
        
        for priority, patterns in self.priority_patterns.items():
            for pattern in patterns:
                if pattern in text_lower:
                    return pattern, "PRIORITY_LEVEL", priority
        
        return None, None, None

    def extract_task_category(self, text: str) -> tuple:
        """Extract TASK_CATEGORY entity"""
        text_lower = text.lower()
        
        for category, patterns in self.category_patterns.items():
            for pattern in patterns:
                if pattern in text_lower:
                    return pattern, "TASK_CATEGORY", category
        
        return None, None, None

    def extract_commitment_level(self, text: str) -> tuple:
        """Extract COMMITMENT_LEVEL entity"""
        text_lower = text.lower()
        
        for commitment, patterns in self.commitment_patterns.items():
            for pattern in patterns:
                if pattern in text_lower:
                    return pattern, "COMMITMENT_LEVEL", commitment
        
        return None, None, None

    def extract_temporal_modifier(self, text: str) -> tuple:
        """Extract TEMPORAL_MODIFIER entity"""
        text_lower = text.lower()
        
        for modifier in self.temporal_modifiers:
            if modifier in text_lower:
                return modifier, "TEMPORAL_MODIFIER", modifier
        
        return None, None, None

    def extract_intent_type(self, text: str) -> tuple:
        """Extract INTENT_TYPE entity"""
        text_lower = text.lower()
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if pattern in text_lower:
                    return pattern, "INTENT_TYPE", intent
        
        return None, None, None

    def extract_measurable_target(self, text: str) -> tuple:
        """Extract MEASURABLE_TARGET entity"""
        # Look for patterns like "two chapters", "30 minutes", "5 exercises"
        patterns = [
            r'\b(two|three|four|five|six|seven|eight|nine|ten|\d+)\s+(chapters?|pages?|minutes?|hours?|exercises?|modules?)\b',
            r'\b\d+\s+(chapters?|pages?|minutes?|hours?|exercises?|modules?)\b',
            r'\bup to (chapter|page|module|level)\s+(\w+)\b'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0), "MEASURABLE_TARGET", match.group(0)
        
        return None, None, None

    def extract_location_context(self, text: str) -> tuple:
        """Extract LOCATION_CONTEXT entity"""
        location_patterns = [
            r'\bat (gym|home|office|university|library|cafe)\b',
            r'\bin the (kitchen|bedroom|garden|park)\b',
            r'\bfrom (home|office)\b'
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0), "LOCATION_CONTEXT", match.group(0)
        
        return None, None, None

    def extract_entities_from_task(self, task_text: str, task_start: int) -> List[Dict[str, str]]:
        """Extract all entities from a single task"""
        entities = []
        
        # Extract each type of entity
        extractors = [
            self.extract_task_action,
            self.extract_priority_level,
            self.extract_task_category,
            self.extract_commitment_level,
            self.extract_temporal_modifier,
            self.extract_intent_type,
            self.extract_measurable_target,
            self.extract_location_context
        ]
        
        action_word = None
        
        for extractor in extractors:
            if extractor == self.extract_task_action:
                word, entity_name, value = extractor(task_text)
                action_word = value
            elif extractor == self.extract_task_object:
                word, entity_name, value = extractor(task_text, action_word)
            else:
                word, entity_name, value = extractor(task_text)
            
            if word and entity_name and value:
                start_char = task_text.find(word)
                if start_char != -1:
                    abs_start = task_start + start_char
                    abs_end = abs_start + len(word)
                    entities.append({
                        "start_char": abs_start,
                        "end_char": abs_end,
                        "entity_name": entity_name,
                        "value": value
                    })
        
        # Extract task object separately since it depends on action
        if action_word:
            word, entity_name, value = self.extract_task_object(task_text, action_word)
            if word and entity_name and value:
                start_char = task_text.find(word)
                if start_char != -1:
                    abs_start = task_start + start_char
                    abs_end = abs_start + len(word)
                    entities.append({
                        "start_char": abs_start,
                        "end_char": abs_end,
                        "entity_name": entity_name,
                        "value": value
                    })
        return entities


    def process_text(self, text: str) -> List[Dict[str, Any]]:
        """Main method to process text and extract all entities"""
        # Extract individual tasks
        tasks = self.extract_tasks_from_text(text)
        
        result = {"text": text, "entities": []}
    
        for task_text, task_start in tasks:
            entities = self.extract_entities_from_task(task_text, task_start)
            
            if entities:  # Only include tasks that have entities
                result["entities"].extend(entities)
        
        return result


# Example usage
def main():
    extractor = TaskEntityExtractor()
    
    # Sample text
    sample_text = """Today I have two important tasks: talk to Sara and Nick about the prototype,
    and start making a website for self-management. 
    I also need to start the Applied Plotting course today, 
    complete two basic philosophy chapters,
    and go to the gym for entertainment."""
    
    result = extractor.process_text(sample_text)
    
    # Print results
    for entity in result["entities"]:
        print("Entities:")
        print(f"  - Start_char: '{entity['start_char']}' | Entity: {entity['entity_name']} | Value: {entity['value']}")
            
            
    # store result as json file 
    import json
    # generate unique name by date and time 
    import datetime
    now = datetime.datetime.now()
    filename = now.strftime("%Y-%m-%d_%H-%M-%S") + ".json"
    with open(filename, 'w') as f:
        json.dump(result, f, indent=4)
            

if __name__ == "__main__":
    main()