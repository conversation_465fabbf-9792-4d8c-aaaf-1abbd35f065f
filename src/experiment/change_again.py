import pandas as pd
import os
# read full path to csv file
full_path = os.path.join(os.path.dirname(__file__), 'course_major.csv')
full_path_two = os.path.join(os.path.dirname(__file__), 'students.csv')
pd.set_option("display.max_rows", None)      # show all rows
pd.set_option("display.max_columns", None)   # show all columns
pd.set_option("display.width", None)         # don't wrap columns
pd.set_option("display.max_colwidth", None)  # show full cell content

df = pd.read_csv(full_path)

# drop duplicate rows
df = df.drop_duplicates()

# save back to CSV
df.to_csv("your_file_clean.csv", index=False)