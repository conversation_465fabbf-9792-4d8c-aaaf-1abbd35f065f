"""
SAMPLE CODE - FastAPI application for scheduler NLP service
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import spacy
import logging
from datetime import datetime
import uvicorn

# Import custom components
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pipeline.ner_component import SchedulerNERComponent
from pipeline.rel_component import SchedulerRelationComponent
from pipeline.textcat_component import SchedulerTextCatComponent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Scheduler NLP API",
    description="Natural Language Processing API for intelligent task scheduling",
    version="1.0.0"
)

# Global NLP pipeline
nlp_pipeline = None


class SchedulingRequest(BaseModel):
    """Request model for scheduling text analysis."""
    text: str
    include_entities: bool = True
    include_relations: bool = True
    include_classification: bool = True


class EntityResponse(BaseModel):
    """Response model for extracted entities."""
    text: str
    label: str
    start: int
    end: int
    description: str


class RelationResponse(BaseModel):
    """Response model for extracted relations."""
    head_entity: Dict[str, Any]
    tail_entity: Dict[str, Any]
    relation: str
    confidence: float


class ClassificationResponse(BaseModel):
    """Response model for text classification."""
    primary_category: str
    confidence: float
    all_categories: Dict[str, float]
    priority: str


class SchedulingResponse(BaseModel):
    """Complete response model for scheduling analysis."""
    text: str
    entities: Optional[List[EntityResponse]] = None
    relations: Optional[List[RelationResponse]] = None
    classification: Optional[ClassificationResponse] = None
    processing_time: float
    timestamp: str


class BatchRequest(BaseModel):
    """Request model for batch processing."""
    texts: List[str]
    include_entities: bool = True
    include_relations: bool = True
    include_classification: bool = True


def initialize_nlp_pipeline():
    """Initialize the spaCy NLP pipeline with custom components."""
    global nlp_pipeline
    
    try:
        # Load base model
        nlp_pipeline = spacy.load("en_core_web_sm")
        logger.info("Loaded base spaCy model")
        
        # Add custom components
        ner_component = SchedulerNERComponent(nlp_pipeline)
        nlp_pipeline.add_pipe(ner_component, name="scheduler_ner", last=True)
        
        rel_component = SchedulerRelationComponent(nlp_pipeline)
        nlp_pipeline.add_pipe(rel_component, name="scheduler_relations", last=True)
        
        textcat_component = SchedulerTextCatComponent(nlp_pipeline)
        nlp_pipeline.add_pipe(textcat_component, name="scheduler_textcat", last=True)
        
        logger.info("Initialized NLP pipeline with custom components")
        
    except Exception as e:
        logger.error(f"Failed to initialize NLP pipeline: {e}")
        raise


@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    logger.info("Starting Scheduler NLP API...")
    initialize_nlp_pipeline()
    logger.info("API ready to serve requests")


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Scheduler NLP API",
        "version": "1.0.0",
        "description": "Natural Language Processing for intelligent task scheduling",
        "endpoints": {
            "/analyze": "Analyze scheduling text",
            "/batch": "Batch analyze multiple texts",
            "/health": "Health check",
            "/docs": "API documentation"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "pipeline_loaded": nlp_pipeline is not None
    }


@app.post("/analyze", response_model=SchedulingResponse)
async def analyze_scheduling_text(request: SchedulingRequest):
    """
    Analyze scheduling text and extract entities, relations, and classification.
    
    Args:
        request: SchedulingRequest with text and processing options
        
    Returns:
        SchedulingResponse with analysis results
    """
    if not nlp_pipeline:
        raise HTTPException(status_code=500, detail="NLP pipeline not initialized")
    
    start_time = datetime.now()
    
    try:
        # Process text through pipeline
        doc = nlp_pipeline(request.text)
        
        # Extract entities
        entities = None
        if request.include_entities:
            ner_component = nlp_pipeline.get_pipe("scheduler_ner")
            entity_info = ner_component.get_entity_info(doc)
            entities = [
                EntityResponse(
                    text=ent['text'],
                    label=ent['label'],
                    start=ent['start'],
                    end=ent['end'],
                    description=ent['description']
                ) for ent in entity_info
            ]
        
        # Extract relations
        relations = None
        if request.include_relations:
            relation_data = doc._.relations
            relations = [
                RelationResponse(
                    head_entity=rel['head_entity'],
                    tail_entity=rel['tail_entity'],
                    relation=rel['relation'],
                    confidence=rel['confidence']
                ) for rel in relation_data
            ]
        
        # Get classification
        classification = None
        if request.include_classification:
            textcat_component = nlp_pipeline.get_pipe("scheduler_textcat")
            class_details = textcat_component.get_classification_details(doc)
            classification = ClassificationResponse(
                primary_category=class_details['primary_category'],
                confidence=class_details['confidence'],
                all_categories=class_details['all_categories'],
                priority=class_details['priority']
            )
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SchedulingResponse(
            text=request.text,
            entities=entities,
            relations=relations,
            classification=classification,
            processing_time=processing_time,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error processing text: {e}")
        raise HTTPException(status_code=500, detail=f"Processing error: {str(e)}")


@app.post("/batch")
async def batch_analyze(request: BatchRequest):
    """
    Batch analyze multiple scheduling texts.
    
    Args:
        request: BatchRequest with list of texts and processing options
        
    Returns:
        List of analysis results
    """
    if not nlp_pipeline:
        raise HTTPException(status_code=500, detail="NLP pipeline not initialized")
    
    if len(request.texts) > 100:
        raise HTTPException(status_code=400, detail="Maximum 100 texts per batch")
    
    start_time = datetime.now()
    results = []
    
    try:
        for text in request.texts:
            # Create individual request
            individual_request = SchedulingRequest(
                text=text,
                include_entities=request.include_entities,
                include_relations=request.include_relations,
                include_classification=request.include_classification
            )
            
            # Process individual text
            result = await analyze_scheduling_text(individual_request)
            results.append(result)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return {
            "results": results,
            "batch_size": len(request.texts),
            "total_processing_time": processing_time,
            "average_processing_time": processing_time / len(request.texts),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in batch processing: {e}")
        raise HTTPException(status_code=500, detail=f"Batch processing error: {str(e)}")


@app.get("/categories")
async def get_categories():
    """Get available scheduling categories."""
    if not nlp_pipeline:
        raise HTTPException(status_code=500, detail="NLP pipeline not initialized")
    
    textcat_component = nlp_pipeline.get_pipe("scheduler_textcat")
    return {
        "categories": textcat_component.categories,
        "total_categories": len(textcat_component.categories)
    }


@app.get("/entities")
async def get_entity_types():
    """Get available entity types."""
    if not nlp_pipeline:
        raise HTTPException(status_code=500, detail="NLP pipeline not initialized")
    
    ner_component = nlp_pipeline.get_pipe("scheduler_ner")
    return {
        "entity_types": ner_component.entity_types,
        "total_types": len(ner_component.entity_types)
    }


@app.get("/relations")
async def get_relation_types():
    """Get available relation types."""
    if not nlp_pipeline:
        raise HTTPException(status_code=500, detail="NLP pipeline not initialized")
    
    rel_component = nlp_pipeline.get_pipe("scheduler_relations")
    return {
        "relation_types": rel_component.relation_types,
        "total_types": len(rel_component.relation_types)
    }


if __name__ == "__main__":
    # Run the API server
    uvicorn.run(
        "scheduler_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
