"""
SAMPLE CODE - Command Line Interface demo for scheduler NLP
"""

import argparse
import sys
import os
import json
from typing import Dict, Any
import spacy
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pipeline.ner_component import SchedulerNERComponent
from pipeline.rel_component import SchedulerRelationComponent
from pipeline.textcat_component import SchedulerTextCatComponent
from preprocessing.text_cleaner import TextCleaner


class SchedulerNLPDemo:
    """Command line demo for scheduler NLP pipeline."""
    
    def __init__(self):
        """Initialize the demo with NLP pipeline."""
        self.nlp = None
        self.text_cleaner = TextCleaner()
        self.initialize_pipeline()
    
    def initialize_pipeline(self):
        """Initialize the spaCy NLP pipeline."""
        try:
            # Load base model
            self.nlp = spacy.load("en_core_web_sm")
            print("✓ Loaded base spaCy model")
            
            # Add custom components
            ner_component = SchedulerNERComponent(self.nlp)
            self.nlp.add_pipe(ner_component, name="scheduler_ner", last=True)
            
            rel_component = SchedulerRelationComponent(self.nlp)
            self.nlp.add_pipe(rel_component, name="scheduler_relations", last=True)
            
            textcat_component = SchedulerTextCatComponent(self.nlp)
            self.nlp.add_pipe(textcat_component, name="scheduler_textcat", last=True)
            
            print("✓ Initialized custom NLP components")
            
        except Exception as e:
            print(f"✗ Failed to initialize NLP pipeline: {e}")
            sys.exit(1)
    
    def analyze_text(self, text: str, verbose: bool = False) -> Dict[str, Any]:
        """
        Analyze scheduling text and return results.
        
        Args:
            text: Input text to analyze
            verbose: Whether to include detailed information
            
        Returns:
            Dictionary with analysis results
        """
        # Preprocess text
        preprocessed = self.text_cleaner.preprocess_for_training(text)
        
        # Process through NLP pipeline
        doc = self.nlp(preprocessed['normalized'])
        
        # Extract entities
        ner_component = self.nlp.get_pipe("scheduler_ner")
        entities = ner_component.get_entity_info(doc)
        
        # Extract relations
        relations = doc._.relations
        
        # Get classification
        textcat_component = self.nlp.get_pipe("scheduler_textcat")
        classification = textcat_component.get_classification_details(doc)
        
        # Get relation summary
        rel_component = self.nlp.get_pipe("scheduler_relations")
        relation_summary = rel_component.get_relations_summary(doc)
        
        results = {
            'original_text': text,
            'preprocessed': preprocessed if verbose else None,
            'entities': entities,
            'relations': relations,
            'relation_summary': relation_summary,
            'classification': classification,
            'processing_info': {
                'timestamp': datetime.now().isoformat(),
                'pipeline_components': self.nlp.pipe_names
            }
        }
        
        return results
    
    def print_results(self, results: Dict[str, Any], format_type: str = "pretty"):
        """
        Print analysis results in specified format.
        
        Args:
            results: Analysis results dictionary
            format_type: Output format ('pretty', 'json', 'compact')
        """
        if format_type == "json":
            print(json.dumps(results, indent=2, default=str))
            return
        
        text = results['original_text']
        entities = results['entities']
        relations = results['relations']
        classification = results['classification']
        
        if format_type == "compact":
            print(f"Text: {text}")
            print(f"Category: {classification['primary_category']} ({classification['confidence']:.2f})")
            print(f"Entities: {len(entities)}, Relations: {len(relations)}")
            return
        
        # Pretty format (default)
        print("=" * 80)
        print("SCHEDULER NLP ANALYSIS RESULTS")
        print("=" * 80)
        
        print(f"\n📝 INPUT TEXT:")
        print(f"   {text}")
        
        print(f"\n🏷️  TEXT CLASSIFICATION:")
        print(f"   Primary Category: {classification['primary_category']}")
        print(f"   Confidence: {classification['confidence']:.2f}")
        print(f"   Priority: {classification['priority']}")
        
        if len(classification['top_3_categories']) > 1:
            print(f"   Top 3 Categories:")
            for i, (cat, score) in enumerate(classification['top_3_categories'][:3], 1):
                print(f"     {i}. {cat}: {score:.2f}")
        
        print(f"\n🎯 NAMED ENTITIES ({len(entities)} found):")
        if entities:
            for i, entity in enumerate(entities, 1):
                print(f"   {i}. '{entity['text']}' → {entity['label']}")
                print(f"      Position: {entity['start']}-{entity['end']}")
                if entity.get('description'):
                    print(f"      Description: {entity['description']}")
        else:
            print("   No entities found")
        
        print(f"\n🔗 RELATIONS ({len(relations)} found):")
        if relations:
            for i, relation in enumerate(relations, 1):
                head = relation['head_entity']
                tail = relation['tail_entity']
                rel_type = relation['relation']
                confidence = relation['confidence']
                
                print(f"   {i}. '{head['text']}' --[{rel_type}]--> '{tail['text']}'")
                print(f"      Confidence: {confidence:.2f}")
        else:
            print("   No relations found")
        
        # Relation summary
        rel_summary = results['relation_summary']
        if rel_summary['total_relations'] > 0:
            print(f"\n📊 RELATION SUMMARY:")
            print(f"   Total Relations: {rel_summary['total_relations']}")
            print(f"   Relation Types: {dict(rel_summary['relation_types'])}")
            print(f"   High Confidence Relations: {len(rel_summary['high_confidence_relations'])}")
            print(f"   Entities Involved: {len(rel_summary['entities_involved'])}")
    
    def interactive_mode(self):
        """Run interactive mode for continuous text analysis."""
        print("🤖 Scheduler NLP Interactive Demo")
        print("=" * 50)
        print("Enter scheduling text to analyze (type 'quit' to exit)")
        print("Commands:")
        print("  'help' - Show this help")
        print("  'examples' - Show example texts")
        print("  'verbose on/off' - Toggle verbose output")
        print("  'format pretty/json/compact' - Change output format")
        print()
        
        verbose = False
        output_format = "pretty"
        
        while True:
            try:
                user_input = input("\n📝 Enter text: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                elif user_input.lower() == 'help':
                    print("\nCommands:")
                    print("  'help' - Show this help")
                    print("  'examples' - Show example texts")
                    print("  'verbose on/off' - Toggle verbose output")
                    print("  'format pretty/json/compact' - Change output format")
                    continue
                
                elif user_input.lower() == 'examples':
                    examples = [
                        "Schedule a meeting with John Smith tomorrow at 2 PM",
                        "Remind me to call the client next Friday before 5 PM",
                        "Cancel my appointment with Dr. Johnson on Thursday",
                        "Set up a recurring weekly team standup every Monday at 9 AM",
                        "The project deadline is December 15th"
                    ]
                    print("\n📋 Example texts:")
                    for i, example in enumerate(examples, 1):
                        print(f"  {i}. {example}")
                    continue
                
                elif user_input.lower().startswith('verbose'):
                    if 'on' in user_input.lower():
                        verbose = True
                        print("✓ Verbose mode enabled")
                    else:
                        verbose = False
                        print("✓ Verbose mode disabled")
                    continue
                
                elif user_input.lower().startswith('format'):
                    if 'json' in user_input.lower():
                        output_format = 'json'
                    elif 'compact' in user_input.lower():
                        output_format = 'compact'
                    else:
                        output_format = 'pretty'
                    print(f"✓ Output format set to: {output_format}")
                    continue
                
                elif not user_input:
                    continue
                
                # Analyze the text
                print("\n🔄 Processing...")
                results = self.analyze_text(user_input, verbose=verbose)
                self.print_results(results, format_type=output_format)
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")


def main():
    """Main function for CLI demo."""
    parser = argparse.ArgumentParser(
        description="Scheduler NLP Demo - Analyze scheduling text with NLP"
    )
    parser.add_argument(
        "text",
        nargs="?",
        help="Text to analyze (if not provided, starts interactive mode)"
    )
    parser.add_argument(
        "--format",
        choices=["pretty", "json", "compact"],
        default="pretty",
        help="Output format"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Include detailed preprocessing information"
    )
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Start interactive mode"
    )
    
    args = parser.parse_args()
    
    # Initialize demo
    demo = SchedulerNLPDemo()
    
    if args.interactive or not args.text:
        # Interactive mode
        demo.interactive_mode()
    else:
        # Single text analysis
        print("🔄 Analyzing text...")
        results = demo.analyze_text(args.text, verbose=args.verbose)
        demo.print_results(results, format_type=args.format)


if __name__ == "__main__":
    main()
