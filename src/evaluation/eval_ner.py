"""
SAMPLE CODE - Named Entity Recognition evaluation script
"""

import spacy
from spacy.training import Example
from spacy.scorer import Scorer
import json
from pathlib import Path
from typing import List, Dict, Tuple, Any
import logging
from collections import defaultdict

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NEREvaluator:
    """Evaluator for Named Entity Recognition models."""
    
    def __init__(self, model_path: str):
        """
        Initialize NER evaluator.
        
        Args:
            model_path: Path to trained NER model
        """
        self.model_path = model_path
        self.nlp = None
        self.load_model()
    
    def load_model(self):
        """Load the trained NER model."""
        try:
            self.nlp = spacy.load(self.model_path)
            logger.info(f"Loaded NER model from {self.model_path}")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def load_test_data(self, data_path: str) -> List[Tuple[str, Dict]]:
        """
        Load test data from JSON file.
        
        Args:
            data_path: Path to test data file
            
        Returns:
            List of (text, annotations) tuples
        """
        with open(data_path, 'r') as f:
            data = json.load(f)
        
        test_data = []
        for item in data:
            text = item['text']
            entities = []
            
            for entity in item.get('entities', []):
                entities.append((
                    entity['start'],
                    entity['end'],
                    entity['label']
                ))
            
            annotations = {'entities': entities}
            test_data.append((text, annotations))
        
        return test_data
    
    def evaluate(self, test_data: List[Tuple[str, Dict]]) -> Dict[str, Any]:
        """
        Evaluate the NER model on test data.
        
        Args:
            test_data: List of (text, annotations) tuples
            
        Returns:
            Dictionary with evaluation metrics
        """
        examples = []
        for text, annotations in test_data:
            doc = self.nlp.make_doc(text)
            example = Example.from_dict(doc, annotations)
            examples.append(example)
        
        # Use spaCy's built-in scorer
        scorer = Scorer()
        scores = scorer.score(examples)
        
        return scores
    
    def detailed_evaluation(self, test_data: List[Tuple[str, Dict]]) -> Dict[str, Any]:
        """
        Perform detailed evaluation with per-entity-type metrics.
        
        Args:
            test_data: List of (text, annotations) tuples
            
        Returns:
            Dictionary with detailed evaluation metrics
        """
        # Track predictions and gold standard
        predictions = defaultdict(list)
        gold_standard = defaultdict(list)
        
        # Entity-level metrics
        entity_metrics = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})
        
        for text, annotations in test_data:
            doc = self.nlp(text)
            
            # Get predicted entities
            pred_entities = [(ent.start_char, ent.end_char, ent.label_) for ent in doc.ents]
            
            # Get gold entities
            gold_entities = []
            for start, end, label in annotations['entities']:
                gold_entities.append((start, end, label))
            
            # Calculate entity-level metrics
            for pred_ent in pred_entities:
                start, end, label = pred_ent
                predictions[label].append((start, end, text))
                
                if pred_ent in gold_entities:
                    entity_metrics[label]['tp'] += 1
                else:
                    entity_metrics[label]['fp'] += 1
            
            for gold_ent in gold_entities:
                start, end, label = gold_ent
                gold_standard[label].append((start, end, text))
                
                if gold_ent not in pred_entities:
                    entity_metrics[label]['fn'] += 1
        
        # Calculate precision, recall, F1 for each entity type
        detailed_metrics = {}
        overall_tp = overall_fp = overall_fn = 0
        
        for label in set(list(predictions.keys()) + list(gold_standard.keys())):
            tp = entity_metrics[label]['tp']
            fp = entity_metrics[label]['fp']
            fn = entity_metrics[label]['fn']
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            detailed_metrics[label] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'support': len(gold_standard[label]),
                'predicted': len(predictions[label]),
                'true_positives': tp,
                'false_positives': fp,
                'false_negatives': fn
            }
            
            overall_tp += tp
            overall_fp += fp
            overall_fn += fn
        
        # Calculate overall metrics
        overall_precision = overall_tp / (overall_tp + overall_fp) if (overall_tp + overall_fp) > 0 else 0
        overall_recall = overall_tp / (overall_tp + overall_fn) if (overall_tp + overall_fn) > 0 else 0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
        
        return {
            'overall_metrics': {
                'precision': overall_precision,
                'recall': overall_recall,
                'f1': overall_f1,
                'total_entities_gold': overall_tp + overall_fn,
                'total_entities_predicted': overall_tp + overall_fp,
                'true_positives': overall_tp,
                'false_positives': overall_fp,
                'false_negatives': overall_fn
            },
            'per_entity_metrics': detailed_metrics,
            'entity_distribution': {
                'gold': {label: len(entities) for label, entities in gold_standard.items()},
                'predicted': {label: len(entities) for label, entities in predictions.items()}
            }
        }
    
    def error_analysis(self, test_data: List[Tuple[str, Dict]], max_errors: int = 10) -> Dict[str, Any]:
        """
        Perform error analysis on test data.
        
        Args:
            test_data: List of (text, annotations) tuples
            max_errors: Maximum number of errors to report per type
            
        Returns:
            Dictionary with error analysis
        """
        false_positives = []
        false_negatives = []
        
        for text, annotations in test_data:
            doc = self.nlp(text)
            
            # Get predicted and gold entities
            pred_entities = set((ent.start_char, ent.end_char, ent.label_) for ent in doc.ents)
            gold_entities = set()
            
            for start, end, label in annotations['entities']:
                gold_entities.add((start, end, label))
            
            # Find false positives (predicted but not in gold)
            for pred_ent in pred_entities:
                if pred_ent not in gold_entities:
                    start, end, label = pred_ent
                    false_positives.append({
                        'text': text,
                        'entity_text': text[start:end],
                        'predicted_label': label,
                        'start': start,
                        'end': end,
                        'context': text[max(0, start-20):min(len(text), end+20)]
                    })
            
            # Find false negatives (in gold but not predicted)
            for gold_ent in gold_entities:
                if gold_ent not in pred_entities:
                    start, end, label = gold_ent
                    false_negatives.append({
                        'text': text,
                        'entity_text': text[start:end],
                        'gold_label': label,
                        'start': start,
                        'end': end,
                        'context': text[max(0, start-20):min(len(text), end+20)]
                    })
        
        return {
            'false_positives': false_positives[:max_errors],
            'false_negatives': false_negatives[:max_errors],
            'total_false_positives': len(false_positives),
            'total_false_negatives': len(false_negatives)
        }
    
    def generate_report(self, test_data: List[Tuple[str, Dict]], output_path: str = None) -> str:
        """
        Generate a comprehensive evaluation report.
        
        Args:
            test_data: List of (text, annotations) tuples
            output_path: Optional path to save report
            
        Returns:
            Report as string
        """
        # Run evaluations
        basic_scores = self.evaluate(test_data)
        detailed_metrics = self.detailed_evaluation(test_data)
        error_analysis = self.error_analysis(test_data)
        
        # Generate report
        report = []
        report.append("=" * 80)
        report.append("NER MODEL EVALUATION REPORT")
        report.append("=" * 80)
        report.append(f"Model: {self.model_path}")
        report.append(f"Test samples: {len(test_data)}")
        report.append("")
        
        # Overall metrics
        overall = detailed_metrics['overall_metrics']
        report.append("OVERALL PERFORMANCE:")
        report.append(f"  Precision: {overall['precision']:.3f}")
        report.append(f"  Recall:    {overall['recall']:.3f}")
        report.append(f"  F1-Score:  {overall['f1']:.3f}")
        report.append(f"  Total Gold Entities: {overall['total_entities_gold']}")
        report.append(f"  Total Predicted:     {overall['total_entities_predicted']}")
        report.append("")
        
        # Per-entity metrics
        report.append("PER-ENTITY PERFORMANCE:")
        per_entity = detailed_metrics['per_entity_metrics']
        report.append(f"{'Entity':<15} {'Precision':<10} {'Recall':<10} {'F1':<10} {'Support':<10}")
        report.append("-" * 60)
        
        for entity, metrics in sorted(per_entity.items()):
            report.append(f"{entity:<15} {metrics['precision']:<10.3f} {metrics['recall']:<10.3f} "
                         f"{metrics['f1']:<10.3f} {metrics['support']:<10}")
        
        report.append("")
        
        # Error analysis
        report.append("ERROR ANALYSIS:")
        report.append(f"  Total False Positives: {error_analysis['total_false_positives']}")
        report.append(f"  Total False Negatives: {error_analysis['total_false_negatives']}")
        
        if error_analysis['false_positives']:
            report.append("\n  Sample False Positives:")
            for i, fp in enumerate(error_analysis['false_positives'][:5], 1):
                report.append(f"    {i}. '{fp['entity_text']}' → {fp['predicted_label']}")
                report.append(f"       Context: ...{fp['context']}...")
        
        if error_analysis['false_negatives']:
            report.append("\n  Sample False Negatives:")
            for i, fn in enumerate(error_analysis['false_negatives'][:5], 1):
                report.append(f"    {i}. '{fn['entity_text']}' (missed {fn['gold_label']})")
                report.append(f"       Context: ...{fn['context']}...")
        
        report_text = "\n".join(report)
        
        # Save report if path provided
        if output_path:
            with open(output_path, 'w') as f:
                f.write(report_text)
            logger.info(f"Report saved to {output_path}")
        
        return report_text


def main():
    """Main evaluation function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Evaluate NER model")
    parser.add_argument("--model", required=True, help="Path to trained NER model")
    parser.add_argument("--test-data", required=True, help="Path to test data JSON file")
    parser.add_argument("--output", help="Path to save evaluation report")
    
    args = parser.parse_args()
    
    # Initialize evaluator
    evaluator = NEREvaluator(args.model)
    
    # Load test data
    test_data = evaluator.load_test_data(args.test_data)
    logger.info(f"Loaded {len(test_data)} test examples")
    
    # Generate and print report
    report = evaluator.generate_report(test_data, args.output)
    print(report)


if __name__ == "__main__":
    main()
