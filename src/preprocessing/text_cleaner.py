"""
SAMPLE CODE - Text preprocessing and cleaning utilities for scheduler NLP
"""

import re
import string
from typing import List, Dict, Any
import spacy
from spacy.lang.en import English


class TextCleaner:
    """Text preprocessing and cleaning utilities."""
    
    def __init__(self):
        self.nlp = English()
        self.nlp.add_pipe('sentencizer')
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize input text.
        
        Args:
            text: Raw input text
            
        Returns:
            Cleaned text
        """
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Normalize quotes
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        
        # Remove control characters
        text = ''.join(char for char in text if ord(char) >= 32)
        
        return text
    
    def tokenize(self, text: str) -> List[str]:
        """
        Tokenize text into words.
        
        Args:
            text: Input text
            
        Returns:
            List of tokens
        """
        doc = self.nlp(text)
        return [token.text for token in doc]
    
    def extract_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences.
        
        Args:
            text: Input text
            
        Returns:
            List of sentences
        """
        doc = self.nlp(text)
        return [sent.text.strip() for sent in doc.sents]
    
    def normalize_datetime_expressions(self, text: str) -> str:
        """
        Normalize common datetime expressions.
        
        Args:
            text: Input text
            
        Returns:
            Text with normalized datetime expressions
        """
        # Common datetime normalizations
        replacements = {
            r'\btomorrow\b': 'tomorrow',
            r'\byesterday\b': 'yesterday',
            r'\btoday\b': 'today',
            r'\bnext week\b': 'next week',
            r'\blast week\b': 'last week',
            r'\bthis week\b': 'this week',
            r'\bnext month\b': 'next month',
            r'\blast month\b': 'last month',
            r'\bthis month\b': 'this month',
        }
        
        for pattern, replacement in replacements.items():
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        
        return text
    
    def preprocess_for_training(self, text: str) -> Dict[str, Any]:
        """
        Preprocess text for model training.
        
        Args:
            text: Raw input text
            
        Returns:
            Dictionary with preprocessed data
        """
        cleaned = self.clean_text(text)
        normalized = self.normalize_datetime_expressions(cleaned)
        tokens = self.tokenize(normalized)
        sentences = self.extract_sentences(normalized)
        
        return {
            'original': text,
            'cleaned': cleaned,
            'normalized': normalized,
            'tokens': tokens,
            'sentences': sentences,
            'token_count': len(tokens),
            'sentence_count': len(sentences)
        }


def batch_preprocess(texts: List[str]) -> List[Dict[str, Any]]:
    """
    Preprocess a batch of texts.
    
    Args:
        texts: List of raw texts
        
    Returns:
        List of preprocessed data dictionaries
    """
    cleaner = TextCleaner()
    return [cleaner.preprocess_for_training(text) for text in texts]


if __name__ == "__main__":
    # Example usage
    cleaner = TextCleaner()
    
    sample_text = "Schedule a meeting with John tomorrow at 2 PM in the conference room."
    result = cleaner.preprocess_for_training(sample_text)
    
    print("Preprocessing result:")
    for key, value in result.items():
        print(f"{key}: {value}")
