# Project Scheduler NLP

## SAMPLE CODE - Project Overview

This project implements a Natural Language Processing pipeline for intelligent task scheduling and project management. It uses spaCy for Named Entity Recognition (NER), Relation Extraction (RE), and Text Classification to understand and process scheduling requests.

## Features

- **Named Entity Recognition**: Identifies dates, times, people, tasks, and locations
- **Relation Extraction**: Understands relationships between entities (e.g., "meeting with <PERSON> on Friday")
- **Text Classification**: Categorizes requests by priority, type, and urgency
- **API Integration**: RESTful API for integration with scheduling systems
- **CLI Demo**: Command-line interface for testing

## Setup Instructions

### Prerequisites
- Python 3.8+
- pip or conda

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd project-scheduler-nlp
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Download spaCy language model:
```bash
python -m spacy download en_core_web_sm
```

### Training Models

1. Prepare your annotated data in the `data/annotated/` directories
2. Train the models:
```bash
python src/training/train_ner.py
python src/training/train_rel.py
python src/training/train_textcat.py
```

### Running the Application

#### API Server
```bash
python src/app/scheduler_api.py
```

#### CLI Demo
```bash
python src/app/demo_cli.py "Schedule a meeting with John tomorrow at 2 PM"
```

## Project Structure

- `config/`: Training configurations for spaCy models
- `data/`: Raw, annotated, and processed datasets
- `src/`: Source code for preprocessing, training, and application
- `models/`: Trained model artifacts
- `tests/`: Unit tests
- `logs/`: Training and evaluation logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License
