"""
Setup script for Scheduler NLP project.

This file configures the Python package for installation and distribution.

Usage:
    pip install -e .              # Install in development mode
    pip install -e .[dev]         # Install with development dependencies
    pip install -e .[ml,viz]      # Install with ML and visualization extras

After installation, console commands become available:
    scheduler-demo "text here"     # Run CLI demo
    scheduler-api                  # Start API server

The setup.py enables:
- Package installation with proper dependency management
- Console scripts for easy command-line access
- Extra dependency groups for different use cases
- Proper package metadata for distribution
"""

from setuptools import setup, find_packages
import os

# Read README for long description
def read_readme():
    with open("README.md", "r", encoding="utf-8") as f:
        return f.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="scheduler-nlp",
    version="0.1.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="Natural Language Processing pipeline for intelligent task scheduling",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/scheduler-nlp",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "black>=23.9.0",
            "flake8>=6.1.0",
            "mypy>=1.6.0",
        ],
        "ml": [
            "torch>=2.1.0",
            "transformers>=4.35.0",
        ],
        "viz": [
            "matplotlib>=3.7.0",
            "seaborn>=0.12.0",
            "plotly>=5.17.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "scheduler-demo=app.demo_cli:main",
            "scheduler-api=app.scheduler_api:main",
        ],
    },
)