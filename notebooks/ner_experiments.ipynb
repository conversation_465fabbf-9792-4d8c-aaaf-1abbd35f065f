{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Named Entity Recognition Experiments\n",
    "\n",
    "**SAMPLE CODE** - This notebook contains experiments and analysis for the NER component of the scheduler NLP pipeline.\n",
    "\n",
    "## Overview\n",
    "This notebook explores:\n",
    "- Entity extraction from scheduling texts\n",
    "- Performance analysis of different NER approaches\n",
    "- Error analysis and improvement strategies\n",
    "- Visualization of entity distributions"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import sys\n",
    "import os\n",
    "sys.path.append('../src')\n",
    "\n",
    "import spacy\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from collections import Counter, defaultdict\n",
    "import json\n",
    "\n",
    "# Import custom components\n",
    "from pipeline.ner_component import SchedulerNERComponent\n",
    "from preprocessing.text_cleaner import TextCleaner\n",
    "\n",
    "# Set up plotting\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "%matplotlib inline"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Initialize NLP Pipeline"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load spaCy model and add custom NER component\n",
    "nlp = spacy.load(\"en_core_web_sm\")\n",
    "ner_component = SchedulerNERComponent(nlp)\n",
    "nlp.add_pipe(ner_component, name=\"scheduler_ner\", last=True)\n",
    "\n",
    "print(\"Pipeline components:\", nlp.pipe_names)\n",
    "print(\"Entity types:\", list(ner_component.entity_types.keys()))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Load and Explore Sample Data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load sample data\n",
    "with open('../data/examples/sample_requests.json', 'r') as f:\n",
    "    sample_data = json.load(f)\n",
    "\n",
    "print(f\"Loaded {len(sample_data)} sample requests\")\n",
    "\n",
    "# Display first few examples\n",
    "for i, item in enumerate(sample_data[:3]):\n",
    "    print(f\"\\nExample {i+1}:\")\n",
    "    print(f\"Text: {item['text']}\")\n",
    "    print(f\"Entities: {len(item.get('entities', []))}\")\n",
    "    print(f\"Category: {item.get('category', 'N/A')}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Entity Extraction Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Extract entities from all sample texts\n",
    "extraction_results = []\n",
    "\n",
    "for item in sample_data:\n",
    "    text = item['text']\n",
    "    doc = nlp(text)\n",
    "    \n",
    "    # Get entity information\n",
    "    entity_info = ner_component.get_entity_info(doc)\n",
    "    \n",
    "    extraction_results.append({\n",
    "        'text': text,\n",
    "        'entities': entity_info,\n",
    "        'entity_count': len(entity_info),\n",
    "        'gold_entities': item.get('entities', []),\n",
    "        'gold_count': len(item.get('entities', []))\n",
    "    })\n",
    "\n",
    "print(\"Entity extraction completed!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Analyze entity distribution\n",
    "entity_counts = Counter()\n",
    "all_entities = []\n",
    "\n",
    "for result in extraction_results:\n",
    "    for entity in result['entities']:\n",
    "        entity_counts[entity['label']] += 1\n",
    "        all_entities.append(entity)\n",
    "\n",
    "print(\"Entity type distribution:\")\n",
    "for entity_type, count in entity_counts.most_common():\n",
    "    print(f\"  {entity_type}: {count}\")\n",
    "\n",
    "# Create DataFrame for analysis\n",
    "entities_df = pd.DataFrame(all_entities)\n",
    "print(f\"\\nTotal entities extracted: {len(entities_df)}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Plot entity type distribution\n",
    "plt.figure(figsize=(12, 6))\n",
    "\n",
    "# Entity counts bar plot\n",
    "plt.subplot(1, 2, 1)\n",
    "entity_types = list(entity_counts.keys())\n",
    "counts = list(entity_counts.values())\n",
    "\n",
    "plt.bar(entity_types, counts)\n",
    "plt.title('Entity Type Distribution')\n",
    "plt.xlabel('Entity Type')\n",
    "plt.ylabel('Count')\n",
    "plt.xticks(rotation=45)\n",
    "\n",
    "# Entity length distribution\n",
    "plt.subplot(1, 2, 2)\n",
    "if not entities_df.empty:\n",
    "    entity_lengths = entities_df['text'].str.len()\n",
    "    plt.hist(entity_lengths, bins=20, alpha=0.7)\n",
    "    plt.title('Entity Text Length Distribution')\n",
    "    plt.xlabel('Character Length')\n",
    "    plt.ylabel('Frequency')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Performance Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Compare predicted vs gold entities\n",
    "comparison_data = []\n",
    "\n",
    "for result in extraction_results:\n",
    "    predicted_count = result['entity_count']\n",
    "    gold_count = result['gold_count']\n",
    "    \n",
    "    comparison_data.append({\n",
    "        'text': result['text'][:50] + '...' if len(result['text']) > 50 else result['text'],\n",
    "        'predicted': predicted_count,\n",
    "        'gold': gold_count,\n",
    "        'difference': predicted_count - gold_count\n",
    "    })\n",
    "\n",
    "comparison_df = pd.DataFrame(comparison_data)\n",
    "print(\"Predicted vs Gold Entity Counts:\")\n",
    "print(comparison_df)\n",
    "\n",
    "# Summary statistics\n",
    "print(f\"\\nSummary:\")\n",
    "print(f\"Average predicted entities per text: {comparison_df['predicted'].mean():.2f}\")\n",
    "print(f\"Average gold entities per text: {comparison_df['gold'].mean():.2f}\")\n",
    "print(f\"Average difference: {comparison_df['difference'].mean():.2f}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Error Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Analyze specific entity extractions\n",
    "print(\"Detailed Entity Analysis:\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "for i, result in enumerate(extraction_results[:3]):\n",
    "    print(f\"\\nText {i+1}: {result['text']}\")\n",
    "    print(\"Extracted entities:\")\n",
    "    \n",
    "    if result['entities']:\n",
    "        for entity in result['entities']:\n",
    "            print(f\"  - '{entity['text']}' → {entity['label']} ({entity['start']}-{entity['end']})\")\n",
    "    else:\n",
    "        print(\"  No entities extracted\")\n",
    "    \n",
    "    print(\"Gold entities:\")\n",
    "    if result['gold_entities']:\n",
    "        for entity in result['gold_entities']:\n",
    "            entity_text = result['text'][entity['start']:entity['end']]\n",
    "            print(f\"  - '{entity_text}' → {entity['label']} ({entity['start']}-{entity['end']})\")\n",
    "    else:\n",
    "        print(\"  No gold entities\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. Experimentation with Different Texts"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test with custom scheduling texts\n",
    "test_texts = [\n",
    "    \"Book a doctor's appointment for next Tuesday at 3:30 PM\",\n",
    "    \"Schedule weekly team meeting every Friday morning\",\n",
    "    \"Remind me to call Sarah before 5 PM today\",\n",
    "    \"Cancel my lunch meeting with the client tomorrow\",\n",
    "    \"Set up a conference call with the London office at 9 AM GMT\"\n",
    "]\n",
    "\n",
    "print(\"Testing with custom texts:\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "for i, text in enumerate(test_texts, 1):\n",
    "    doc = nlp(text)\n",
    "    entities = ner_component.get_entity_info(doc)\n",
    "    \n",
    "    print(f\"\\n{i}. {text}\")\n",
    "    print(f\"   Entities found: {len(entities)}\")\n",
    "    \n",
    "    for entity in entities:\n",
    "        print(f\"   - '{entity['text']}' → {entity['label']}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. Performance Metrics Calculation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Calculate precision, recall, F1 (simplified)\n",
    "def calculate_entity_overlap(predicted_entities, gold_entities, text):\n",
    "    \"\"\"Calculate overlap between predicted and gold entities.\"\"\"\n",
    "    pred_spans = set()\n",
    "    gold_spans = set()\n",
    "    \n",
    "    # Convert predicted entities to spans\n",
    "    for entity in predicted_entities:\n",
    "        pred_spans.add((entity['start'], entity['end'], entity['label']))\n",
    "    \n",
    "    # Convert gold entities to spans\n",
    "    for entity in gold_entities:\n",
    "        gold_spans.add((entity['start'], entity['end'], entity['label']))\n",
    "    \n",
    "    # Calculate overlaps\n",
    "    true_positives = len(pred_spans & gold_spans)\n",
    "    false_positives = len(pred_spans - gold_spans)\n",
    "    false_negatives = len(gold_spans - pred_spans)\n",
    "    \n",
    "    return true_positives, false_positives, false_negatives\n",
    "\n",
    "# Calculate overall metrics\n",
    "total_tp = total_fp = total_fn = 0\n",
    "\n",
    "for result in extraction_results:\n",
    "    tp, fp, fn = calculate_entity_overlap(\n",
    "        result['entities'], \n",
    "        result['gold_entities'], \n",
    "        result['text']\n",
    "    )\n",
    "    total_tp += tp\n",
    "    total_fp += fp\n",
    "    total_fn += fn\n",
    "\n",
    "# Calculate metrics\n",
    "precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0\n",
    "recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0\n",
    "f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0\n",
    "\n",
    "print(\"Overall NER Performance:\")\n",
    "print(f\"Precision: {precision:.3f}\")\n",
    "print(f\"Recall: {recall:.3f}\")\n",
    "print(f\"F1-Score: {f1:.3f}\")\n",
    "print(f\"\\nTrue Positives: {total_tp}\")\n",
    "print(f\"False Positives: {total_fp}\")\n",
    "print(f\"False Negatives: {total_fn}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 9. Conclusions and Next Steps\n",
    "\n",
    "Based on the experiments above, we can:\n",
    "\n",
    "1. **Identify strengths and weaknesses** of the current NER approach\n",
    "2. **Plan improvements** such as:\n",
    "   - Adding more training data\n",
    "   - Improving rule-based patterns\n",
    "   - Fine-tuning entity boundaries\n",
    "3. **Optimize for specific entity types** that are most important for scheduling\n",
    "\n",
    "### Recommendations:\n",
    "- Focus on improving TIME and DATE entity extraction\n",
    "- Add more sophisticated PERSON name recognition\n",
    "- Enhance LOCATION detection for meeting venues\n",
    "- Consider using transformer-based models for better performance"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Save results for further analysis\n",
    "results_summary = {\n",
    "    'total_texts': len(extraction_results),\n",
    "    'total_entities_extracted': len(all_entities),\n",
    "    'entity_type_distribution': dict(entity_counts),\n",
    "    'performance_metrics': {\n",
    "        'precision': precision,\n",
    "        'recall': recall,\n",
    "        'f1_score': f1\n",
    "    }\n",
    "}\n",
    "\n",
    "print(\"Experiment completed!\")\n",
    "print(\"Results summary:\", results_summary)"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",\n   "language": "python",\n   "name": "python3"\n  },\n  "language_info": {\n   "codemirror_mode": {\n    "name": "ipython",\n    "version": 3\n   },\n   "file_extension": ".py",\n   "mimetype": "text/x-python",\n   "name": "python",\n   "nbconvert_exporter": "python",\n   "pygments_lexer": "ipython3",\n   "version": "3.8.0"\n  }\n },\n "nbformat": 4,\n "nbformat_minor": 4\n}
